<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\User;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rules;

class UserController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        // In a real implementation, we would fetch users from the database
        // $users = User::latest()->paginate(10);
        
        // For now, we'll create some dummy data
        $users = [
            [
                'id' => 1,
                'name' => 'Admin User',
                'email' => '<EMAIL>',
                'role' => 'admin',
                'created_at' => '2025-01-01 00:00:00',
                'last_login' => '2025-05-05 10:30:00',
                'status' => 'active'
            ],
            [
                'id' => 2,
                'name' => 'Editor User',
                'email' => '<EMAIL>',
                'role' => 'editor',
                'created_at' => '2025-01-02 00:00:00',
                'last_login' => '2025-05-04 15:45:00',
                'status' => 'active'
            ],
            [
                'id' => 3,
                'name' => 'Viewer User',
                'email' => '<EMAIL>',
                'role' => 'viewer',
                'created_at' => '2025-01-03 00:00:00',
                'last_login' => '2025-05-03 09:15:00',
                'status' => 'active'
            ],
            [
                'id' => 4,
                'name' => 'John Doe',
                'email' => '<EMAIL>',
                'role' => 'editor',
                'created_at' => '2025-02-01 00:00:00',
                'last_login' => '2025-05-02 14:20:00',
                'status' => 'active'
            ],
            [
                'id' => 5,
                'name' => 'Jane Smith',
                'email' => '<EMAIL>',
                'role' => 'viewer',
                'created_at' => '2025-02-15 00:00:00',
                'last_login' => '2025-04-30 11:10:00',
                'status' => 'inactive'
            ]
        ];
        
        return view('admin.users.index', compact('users'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin.users.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:users'],
            'password' => ['required', 'confirmed', Rules\Password::defaults()],
            'role' => ['required', 'string', 'in:admin,editor,viewer'],
        ]);

        // In a real implementation, we would create a new user in the database
        // $user = User::create([
        //     'name' => $request->name,
        //     'email' => $request->email,
        //     'password' => Hash::make($request->password),
        //     'role' => $request->role,
        // ]);

        return redirect()->route('admin.users.index')->with('success', 'User created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        // In a real implementation, we would fetch the user from the database
        // $user = User::findOrFail($id);
        
        // For now, we'll create a dummy user
        $user = [
            'id' => $id,
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'role' => 'admin',
            'created_at' => '2025-01-01 00:00:00',
            'last_login' => '2025-05-05 10:30:00',
            'status' => 'active',
            'profile' => [
                'phone' => '+234 ************',
                'department' => 'IT Department',
                'position' => 'System Administrator',
                'bio' => 'Experienced system administrator with expertise in web development and database management.'
            ],
            'permissions' => [
                'manage_users' => true,
                'manage_news' => true,
                'manage_publications' => true,
                'manage_flood_data' => true,
                'manage_data_requests' => true,
                'manage_settings' => true
            ],
            'activity' => [
                [
                    'action' => 'Created news article',
                    'item' => '2025 Annual Flood Outlook Released',
                    'timestamp' => '2025-05-05 09:30:00'
                ],
                [
                    'action' => 'Updated publication',
                    'item' => 'Flood Mitigation & Adaptation Measures',
                    'timestamp' => '2025-05-04 14:15:00'
                ],
                [
                    'action' => 'Approved data request',
                    'item' => 'Request #3 from Robert Johnson',
                    'timestamp' => '2025-05-03 11:45:00'
                ]
            ]
        ];
        
        return view('admin.users.show', compact('user'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        // In a real implementation, we would fetch the user from the database
        // $user = User::findOrFail($id);
        
        // For now, we'll create a dummy user
        $user = [
            'id' => $id,
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'role' => 'admin',
            'created_at' => '2025-01-01 00:00:00',
            'last_login' => '2025-05-05 10:30:00',
            'status' => 'active'
        ];
        
        return view('admin.users.edit', compact('user'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:users,email,' . $id],
            'role' => ['required', 'string', 'in:admin,editor,viewer'],
            'status' => ['required', 'string', 'in:active,inactive'],
        ]);

        // In a real implementation, we would update the user in the database
        // $user = User::findOrFail($id);
        // $user->name = $request->name;
        // $user->email = $request->email;
        // $user->role = $request->role;
        // $user->status = $request->status;
        // $user->save();

        return redirect()->route('admin.users.index')->with('success', 'User updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        // In a real implementation, we would delete the user from the database
        // $user = User::findOrFail($id);
        // $user->delete();

        return redirect()->route('admin.users.index')->with('success', 'User deleted successfully.');
    }
}
