<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\News;
use App\Models\Publication;
use App\Models\Partner;

class HomeController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        // Remove auth middleware for public pages
        // $this->middleware('auth');
    }

    /**
     * Show the application homepage.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function index()
    {
        // In a real implementation, we would fetch data from the database
        // $latestNews = News::latest()->take(3)->get();
        // $featuredPublications = Publication::where('is_featured', true)->take(4)->get();
        // $partners = Partner::orderBy('display_order')->take(6)->get();

        return view('home');
    }

    /**
     * Search for content across the site.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function search(Request $request)
    {
        $query = $request->input('query');

        // In a real implementation, we would search the database
        // $news = News::where('title', 'like', "%{$query}%")
        //     ->orWhere('content', 'like', "%{$query}%")
        //     ->get();
        // $publications = Publication::where('title', 'like', "%{$query}%")
        //     ->orWhere('description', 'like', "%{$query}%")
        //     ->get();

        // For now, we'll create some dummy search results
        $results = [];

        if ($query) {
            // Dummy news results
            $results[] = [
                'type' => 'News',
                'title' => '2025 Annual Flood Outlook Released',
                'description' => 'NIHSA has released the 2025 Annual Flood Outlook (AFO) with predictions for the rainy season.',
                'url' => '#',
                'date' => '2025-05-05'
            ];

            $results[] = [
                'type' => 'Publication',
                'title' => 'Flood Mitigation & Adaptation Measures',
                'description' => 'Guidelines for flood mitigation and adaptation strategies.',
                'url' => '#',
                'date' => '2025-04-15'
            ];

            $results[] = [
                'type' => 'Page',
                'title' => 'About NIHSA',
                'description' => 'Information about the Nigeria Hydrological Services Agency.',
                'url' => route('about'),
                'date' => null
            ];
        }

        return view('search', compact('query', 'results'));
    }

    /**
     * Show the admin dashboard.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function dashboard()
    {
        // This method requires authentication
        $this->middleware('auth');

        // In a real implementation, we would fetch data from the database
        // $newsCount = News::count();
        // $publicationsCount = Publication::count();
        // $dataRequestsCount = DataRequest::count();
        // $pendingDataRequestsCount = DataRequest::where('status', 'pending')->count();
        // $floodDataCount = FloodData::count();
        // $zonalOfficesCount = ZonalOffice::count();
        // $partnersCount = Partner::count();

        // $latestNews = News::latest()->take(5)->get();
        // $latestDataRequests = DataRequest::latest()->take(5)->get();

        // For now, we'll create some dummy data
        $newsCount = 6;
        $publicationsCount = 8;
        $dataRequestsCount = 15;
        $pendingDataRequestsCount = 5;
        $floodDataCount = 120;
        $zonalOfficesCount = 7;
        $partnersCount = 12;

        $latestNews = [
            [
                'id' => 1,
                'title' => '2025 Annual Flood Outlook Released',
                'published_at' => '2025-05-05',
                'category' => 'Press Release'
            ],
            [
                'id' => 2,
                'title' => 'NIHSA Partners with International Organizations',
                'published_at' => '2025-04-28',
                'category' => 'News'
            ],
            [
                'id' => 3,
                'title' => 'New Hydrological Stations Commissioned',
                'published_at' => '2025-04-15',
                'category' => 'News'
            ],
            [
                'id' => 4,
                'title' => 'NIHSA Conducts Flood Awareness Campaign',
                'published_at' => '2025-04-10',
                'category' => 'Event'
            ],
            [
                'id' => 5,
                'title' => 'NIHSA Director General Addresses UN Climate Conference',
                'published_at' => '2025-04-05',
                'category' => 'News'
            ]
        ];

        $latestDataRequests = [
            [
                'id' => 1,
                'name' => 'John Smith',
                'email' => '<EMAIL>',
                'data_type' => 'Flood Data',
                'status' => 'pending',
                'created_at' => '2025-05-05'
            ],
            [
                'id' => 2,
                'name' => 'Jane Doe',
                'email' => '<EMAIL>',
                'data_type' => 'Rainfall Data',
                'status' => 'pending',
                'created_at' => '2025-05-04'
            ],
            [
                'id' => 3,
                'name' => 'Robert Johnson',
                'email' => '<EMAIL>',
                'data_type' => 'Groundwater Data',
                'status' => 'approved',
                'created_at' => '2025-05-03'
            ],
            [
                'id' => 4,
                'name' => 'Emily Williams',
                'email' => '<EMAIL>',
                'data_type' => 'Surface Water Data',
                'status' => 'delivered',
                'created_at' => '2025-05-02'
            ],
            [
                'id' => 5,
                'name' => 'Michael Brown',
                'email' => '<EMAIL>',
                'data_type' => 'Water Quality Data',
                'status' => 'rejected',
                'created_at' => '2025-05-01'
            ]
        ];

        return view('admin.dashboard', compact(
            'newsCount',
            'publicationsCount',
            'dataRequestsCount',
            'pendingDataRequestsCount',
            'floodDataCount',
            'zonalOfficesCount',
            'partnersCount',
            'latestNews',
            'latestDataRequests'
        ));
    }
}
