<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\News;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Auth;

class NewsController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        // In a real implementation, we would fetch data from the database
        // $news = News::latest()->paginate(9);

        // For now, we'll create some dummy data
        $news = [
            [
                'id' => 1,
                'title' => '2025 Annual Flood Outlook Released',
                'content' => 'NIHSA has released the 2025 Annual Flood Outlook (AFO) with predictions for the rainy season. The outlook provides detailed information on potential flood risk areas across Nigeria.',
                'image' => 'https://images.unsplash.com/photo-1574724713425-fee7e2eacf84?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
                'category' => 'Press Release',
                'is_featured' => true,
                'published_at' => '2025-05-05',
                'user_id' => 1
            ],
            [
                'id' => 2,
                'title' => 'NIHSA Partners with International Organizations',
                'content' => 'NIHSA signs MOU with international organizations to improve flood forecasting capabilities. The partnership aims to enhance Nigeria\'s capacity for early flood warning and response.',
                'image' => 'https://images.unsplash.com/photo-1612708771321-1cfe5ce5e106?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
                'category' => 'News',
                'is_featured' => true,
                'published_at' => '2025-04-28',
                'user_id' => 1
            ],
            [
                'id' => 3,
                'title' => 'New Hydrological Stations Commissioned',
                'content' => 'NIHSA commissions 50 new hydrological stations across the country to improve data collection. The new stations will enhance the agency\'s ability to monitor water levels and predict floods.',
                'image' => 'https://images.unsplash.com/photo-1618044733300-9472054094ee?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1471&q=80',
                'category' => 'News',
                'is_featured' => true,
                'published_at' => '2025-04-15',
                'user_id' => 1
            ],
            [
                'id' => 4,
                'title' => 'NIHSA Conducts Flood Awareness Campaign',
                'content' => 'NIHSA launches a nationwide flood awareness campaign to educate communities on flood preparedness and response. The campaign includes workshops, media outreach, and distribution of educational materials.',
                'image' => 'https://images.unsplash.com/photo-1583244685026-d8519b5e3d21?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
                'category' => 'Event',
                'is_featured' => false,
                'published_at' => '2025-04-10',
                'user_id' => 1
            ],
            [
                'id' => 5,
                'title' => 'NIHSA Director General Addresses UN Climate Conference',
                'content' => 'The Director General of NIHSA addresses the UN Climate Conference on Nigeria\'s efforts to mitigate the impacts of climate change on water resources. The address highlights NIHSA\'s role in flood forecasting and management.',
                'image' => 'https://images.unsplash.com/photo-1603202662747-00e33e7d1468?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
                'category' => 'News',
                'is_featured' => false,
                'published_at' => '2025-04-05',
                'user_id' => 1
            ],
            [
                'id' => 6,
                'title' => 'NIHSA Hosts Workshop on Flood Risk Management',
                'content' => 'NIHSA hosts a workshop on flood risk management for stakeholders from various sectors. The workshop focuses on strategies for reducing flood risks and improving response mechanisms.',
                'image' => 'https://images.unsplash.com/photo-1577985043696-8bd54d9f093f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
                'category' => 'Event',
                'is_featured' => false,
                'published_at' => '2025-03-25',
                'user_id' => 1
            ]
        ];

        // Group news by category
        $newsByCategory = [];
        foreach ($news as $item) {
            $newsByCategory[$item['category']][] = $item;
        }

        // Get unique categories for filtering
        $categories = array_keys($newsByCategory);
        sort($categories);

        return view('news.index', compact('news', 'newsByCategory', 'categories'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        // This method is for admin use only
        $this->middleware('auth');

        return view('admin.news.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        // This method is for admin use only
        $this->middleware('auth');

        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'content' => 'required|string',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'category' => 'required|string|max:255',
            'is_featured' => 'boolean',
            'published_at' => 'nullable|date',
        ]);

        // In a real implementation, we would save to the database
        // $imagePath = null;
        // if ($request->hasFile('image')) {
        //     $imagePath = $request->file('image')->store('news');
        // }
        //
        // $news = new News([
        //     'title' => $validated['title'],
        //     'content' => $validated['content'],
        //     'image' => $imagePath,
        //     'category' => $validated['category'],
        //     'is_featured' => $request->has('is_featured'),
        //     'published_at' => $validated['published_at'] ?? now(),
        //     'user_id' => Auth::id(),
        // ]);
        //
        // $news->save();

        return redirect()->route('admin.news.index')->with('success', 'News created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        // In a real implementation, we would fetch from the database
        // $newsItem = News::findOrFail($id);

        // For now, we'll create a dummy news item
        $newsItem = [
            'id' => $id,
            'title' => '2025 Annual Flood Outlook Released',
            'content' => '<p>The Nigeria Hydrological Services Agency (NIHSA) has released the 2025 Annual Flood Outlook (AFO) with predictions for the rainy season. The outlook provides detailed information on potential flood risk areas across Nigeria.</p>

            <p>According to the AFO, several states are at high risk of flooding during the 2025 rainy season, including Lagos, Rivers, Bayelsa, Delta, and Kogi. The outlook also identifies moderate and low-risk areas, providing valuable information for flood preparedness and response.</p>

            <p>The Director-General of NIHSA, in his address during the launch of the AFO, emphasized the importance of early warning and preparedness in mitigating the impacts of floods. He urged state and local governments, as well as communities in flood-prone areas, to take proactive measures to reduce flood risks.</p>

            <p>The AFO is a key tool for flood management in Nigeria, providing stakeholders with information on potential flood scenarios and recommendations for flood mitigation and adaptation. The outlook is based on comprehensive analysis of rainfall patterns, river levels, and other hydrological data.</p>

            <p>NIHSA will continue to monitor the situation and provide updates as necessary. The agency also calls on the public to report any signs of flooding to the appropriate authorities for prompt action.</p>',
            'image' => 'https://images.unsplash.com/photo-1574724713425-fee7e2eacf84?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
            'category' => 'Press Release',
            'is_featured' => true,
            'published_at' => '2025-05-05',
            'user_id' => 1,
            'user' => [
                'name' => 'Admin User'
            ]
        ];

        // Get related news
        // $relatedNews = News::where('id', '!=', $id)
        //     ->where('category', $newsItem->category)
        //     ->latest()
        //     ->take(3)
        //     ->get();

        // For now, we'll create some dummy related news
        $relatedNews = [
            [
                'id' => 2,
                'title' => 'NIHSA Partners with International Organizations',
                'content' => 'NIHSA signs MOU with international organizations to improve flood forecasting capabilities.',
                'image' => 'https://images.unsplash.com/photo-1612708771321-1cfe5ce5e106?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
                'category' => 'News',
                'is_featured' => true,
                'published_at' => '2025-04-28',
                'user_id' => 1
            ],
            [
                'id' => 3,
                'title' => 'New Hydrological Stations Commissioned',
                'content' => 'NIHSA commissions 50 new hydrological stations across the country to improve data collection.',
                'image' => 'https://images.unsplash.com/photo-1618044733300-9472054094ee?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1471&q=80',
                'category' => 'News',
                'is_featured' => true,
                'published_at' => '2025-04-15',
                'user_id' => 1
            ]
        ];

        return view('news.show', compact('newsItem', 'relatedNews'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        // This method is for admin use only
        $this->middleware('auth');

        // $newsItem = News::findOrFail($id);
        // return view('admin.news.edit', compact('newsItem'));

        return redirect()->route('admin.news.index')->with('info', 'This is a demo. In a real implementation, you would be able to edit the news item.');
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        // This method is for admin use only
        $this->middleware('auth');

        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'content' => 'required|string',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'category' => 'required|string|max:255',
            'is_featured' => 'boolean',
            'published_at' => 'nullable|date',
        ]);

        // In a real implementation, we would update the database
        // $newsItem = News::findOrFail($id);
        //
        // $newsItem->title = $validated['title'];
        // $newsItem->content = $validated['content'];
        // $newsItem->category = $validated['category'];
        // $newsItem->is_featured = $request->has('is_featured');
        // $newsItem->published_at = $validated['published_at'] ?? $newsItem->published_at;
        //
        // if ($request->hasFile('image')) {
        //     // Delete old image
        //     if ($newsItem->image) {
        //         Storage::delete($newsItem->image);
        //     }
        //
        //     // Store new image
        //     $newsItem->image = $request->file('image')->store('news');
        // }
        //
        // $newsItem->save();

        return redirect()->route('admin.news.index')->with('success', 'News updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        // This method is for admin use only
        $this->middleware('auth');

        // In a real implementation, we would delete from the database
        // $newsItem = News::findOrFail($id);
        //
        // // Delete the image
        // if ($newsItem->image) {
        //     Storage::delete($newsItem->image);
        // }
        //
        // // Delete the record
        // $newsItem->delete();

        return redirect()->route('admin.news.index')->with('success', 'News deleted successfully.');
    }
}
