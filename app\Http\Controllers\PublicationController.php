<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Publication;
use Illuminate\Support\Facades\Storage;

class PublicationController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        // In a real implementation, we would fetch data from the database
        // $publications = Publication::latest()->paginate(12);

        // For now, we'll create some dummy data
        $publications = [
            [
                'id' => 1,
                'title' => '2025 Annual Flood Outlook',
                'description' => 'Comprehensive flood predictions for the 2025 rainy season.',
                'type' => 'AFO',
                'year' => 2025,
                'publication_date' => '2025-04-01',
                'file_path' => '#',
                'is_featured' => true
            ],
            [
                'id' => 2,
                'title' => 'Flood Mitigation & Adaptation Measures',
                'description' => 'Guidelines for flood mitigation and adaptation strategies.',
                'type' => 'Report',
                'year' => 2025,
                'publication_date' => '2025-04-01',
                'file_path' => '#',
                'is_featured' => true
            ],
            [
                'id' => 3,
                'title' => 'Flood Risk Communication',
                'description' => 'Strategies for effective flood risk communication.',
                'type' => 'Report',
                'year' => 2025,
                'publication_date' => '2025-04-01',
                'file_path' => '#',
                'is_featured' => true
            ],
            [
                'id' => 4,
                'title' => '2024 Annual Flood Outlook',
                'description' => 'Comprehensive flood predictions for the 2024 rainy season.',
                'type' => 'AFO',
                'year' => 2024,
                'publication_date' => '2024-04-01',
                'file_path' => '#',
                'is_featured' => false
            ],
            [
                'id' => 5,
                'title' => '2023 Annual Flood Outlook',
                'description' => 'Comprehensive flood predictions for the 2023 rainy season.',
                'type' => 'AFO',
                'year' => 2023,
                'publication_date' => '2023-04-01',
                'file_path' => '#',
                'is_featured' => false
            ],
            [
                'id' => 6,
                'title' => 'Flood and Drought Bulletin - January 2025',
                'description' => 'Monthly bulletin on flood and drought conditions across Nigeria.',
                'type' => 'Bulletin',
                'year' => 2025,
                'publication_date' => '2025-01-31',
                'file_path' => '#',
                'is_featured' => false
            ],
            [
                'id' => 7,
                'title' => 'Flood and Drought Bulletin - February 2025',
                'description' => 'Monthly bulletin on flood and drought conditions across Nigeria.',
                'type' => 'Bulletin',
                'year' => 2025,
                'publication_date' => '2025-02-28',
                'file_path' => '#',
                'is_featured' => false
            ],
            [
                'id' => 8,
                'title' => 'Flood and Drought Bulletin - March 2025',
                'description' => 'Monthly bulletin on flood and drought conditions across Nigeria.',
                'type' => 'Bulletin',
                'year' => 2025,
                'publication_date' => '2025-03-31',
                'file_path' => '#',
                'is_featured' => false
            ]
        ];

        // Group publications by type
        $publicationsByType = [];
        foreach ($publications as $publication) {
            $publicationsByType[$publication['type']][] = $publication;
        }

        // Get unique years for filtering
        $years = array_unique(array_column($publications, 'year'));
        rsort($years);

        // Get unique types for filtering
        $types = array_keys($publicationsByType);
        sort($types);

        return view('publications.index', compact('publications', 'publicationsByType', 'years', 'types'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        // This method is for admin use only
        $this->middleware('auth');

        return view('admin.publications.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        // This method is for admin use only
        $this->middleware('auth');

        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'file' => 'required|file|mimes:pdf,doc,docx,xls,xlsx,ppt,pptx,zip|max:10240',
            'type' => 'required|string|max:255',
            'year' => 'required|integer|min:1900|max:' . (date('Y') + 1),
            'publication_date' => 'required|date',
            'is_featured' => 'boolean',
        ]);

        // In a real implementation, we would save to the database
        // $filePath = $request->file('file')->store('publications');
        //
        // $publication = new Publication([
        //     'title' => $validated['title'],
        //     'description' => $validated['description'],
        //     'file_path' => $filePath,
        //     'type' => $validated['type'],
        //     'year' => $validated['year'],
        //     'publication_date' => $validated['publication_date'],
        //     'is_featured' => $request->has('is_featured'),
        // ]);
        //
        // $publication->save();

        return redirect()->route('admin.publications.index')->with('success', 'Publication created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        // In a real implementation, we would fetch from the database
        // $publication = Publication::findOrFail($id);

        // For now, we'll create a dummy publication
        $publication = [
            'id' => $id,
            'title' => '2025 Annual Flood Outlook',
            'description' => 'The 2025 Annual Flood Outlook (AFO) provides comprehensive flood predictions for the 2025 rainy season across Nigeria. It includes detailed analysis of potential flood risk areas, rainfall predictions, and recommendations for flood mitigation and preparedness.',
            'type' => 'AFO',
            'year' => 2025,
            'publication_date' => '2025-04-01',
            'file_path' => '#',
            'is_featured' => true
        ];

        return view('publications.show', compact('publication'));
    }

    /**
     * Download a publication file.
     */
    public function download(string $id)
    {
        // In a real implementation, we would fetch from the database
        // $publication = Publication::findOrFail($id);
        // return Storage::download($publication->file_path, $publication->title . '.' . pathinfo($publication->file_path, PATHINFO_EXTENSION));

        // For now, we'll just redirect back with a message
        return redirect()->back()->with('info', 'This is a demo. In a real implementation, the file would be downloaded.');
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        // This method is for admin use only
        $this->middleware('auth');

        // $publication = Publication::findOrFail($id);
        // return view('admin.publications.edit', compact('publication'));

        return redirect()->route('admin.publications.index')->with('info', 'This is a demo. In a real implementation, you would be able to edit the publication.');
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        // This method is for admin use only
        $this->middleware('auth');

        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'file' => 'nullable|file|mimes:pdf,doc,docx,xls,xlsx,ppt,pptx,zip|max:10240',
            'type' => 'required|string|max:255',
            'year' => 'required|integer|min:1900|max:' . (date('Y') + 1),
            'publication_date' => 'required|date',
            'is_featured' => 'boolean',
        ]);

        // In a real implementation, we would update the database
        // $publication = Publication::findOrFail($id);
        //
        // $publication->title = $validated['title'];
        // $publication->description = $validated['description'];
        // $publication->type = $validated['type'];
        // $publication->year = $validated['year'];
        // $publication->publication_date = $validated['publication_date'];
        // $publication->is_featured = $request->has('is_featured');
        //
        // if ($request->hasFile('file')) {
        //     // Delete old file
        //     Storage::delete($publication->file_path);
        //
        //     // Store new file
        //     $publication->file_path = $request->file('file')->store('publications');
        // }
        //
        // $publication->save();

        return redirect()->route('admin.publications.index')->with('success', 'Publication updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        // This method is for admin use only
        $this->middleware('auth');

        // In a real implementation, we would delete from the database
        // $publication = Publication::findOrFail($id);
        //
        // // Delete the file
        // Storage::delete($publication->file_path);
        //
        // // Delete the record
        // $publication->delete();

        return redirect()->route('admin.publications.index')->with('success', 'Publication deleted successfully.');
    }
}
