/* Enhanced Homepage Styles for NIHSA */

/* General Styles */
:root {
    --primary-color: #0056b3;
    --primary-light: #3a7abd;
    --primary-dark: #004494;
    --secondary-color: #28a745;
    --secondary-light: #48c765;
    --secondary-dark: #1e8035;
    --accent-color: #17a2b8;
    --dark-color: #343a40;
    --light-color: #f8f9fa;
    --gray-100: #f8f9fa;
    --gray-200: #e9ecef;
    --gray-300: #dee2e6;
    --gray-400: #ced4da;
    --gray-500: #adb5bd;
    --gray-600: #6c757d;
    --gray-700: #495057;
    --gray-800: #343a40;
    --gray-900: #212529;
}

/* Typography Enhancements */
h1, h2, h3, h4, h5, h6 {
    font-weight: 700;
}

.section-title {
    position: relative;
    margin-bottom: 2.5rem;
    font-size: 2.25rem;
}

.section-title:after {
    content: '';
    position: absolute;
    left: 0;
    bottom: -15px;
    width: 50px;
    height: 4px;
    background: var(--primary-color);
}

.section-title.text-center:after {
    left: 50%;
    transform: translateX(-50%);
}

.text-primary-gradient {
    background: linear-gradient(45deg, var(--primary-color), var(--accent-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

/* Enhanced Hero Section */
.hero-section {
    min-height: 80vh;
    display: flex;
    align-items: center;
    background-attachment: fixed;
    padding: 8rem 0;
    overflow: hidden;
}

.hero-section::before {
    background: linear-gradient(135deg, rgba(0,0,0,0.7) 0%, rgba(0,86,179,0.6) 100%);
}

.hero-content {
    animation: fadeInUp 1s ease-out;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 800;
    margin-bottom: 1.5rem;
    line-height: 1.2;
}

.hero-subtitle {
    font-size: 1.5rem;
    margin-bottom: 2rem;
    font-weight: 400;
}

.hero-search {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    padding: 1rem 1.5rem;
    border-radius: 50px;
    backdrop-filter: blur(5px);
    width: 100%;
    margin-bottom: 2rem;
}

.hero-search::placeholder {
    color: rgba(255, 255, 255, 0.8);
}

.hero-buttons .btn {
    padding: 0.75rem 1.5rem;
    border-radius: 50px;
    font-weight: 600;
    margin-right: 1rem;
    margin-bottom: 1rem;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.hero-buttons .btn-primary {
    background: linear-gradient(45deg, var(--primary-color), var(--primary-light));
    border: none;
}

.hero-buttons .btn-primary:hover {
    background: linear-gradient(45deg, var(--primary-dark), var(--primary-color));
    transform: translateY(-3px);
    box-shadow: 0 6px 15px rgba(0,0,0,0.2);
}

.hero-buttons .btn-outline-light {
    border-width: 2px;
}

.hero-buttons .btn-outline-light:hover {
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateY(-3px);
    box-shadow: 0 6px 15px rgba(0,0,0,0.2);
}

/* Quick Links Section */
.quick-links-section {
    padding: 5rem 0;
    background-color: white;
}

.feature-card {
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0,0,0,0.05);
    transition: all 0.3s ease;
    height: 100%;
    position: relative;
    z-index: 1;
}

.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
}

.feature-card .card-body {
    padding: 2rem;
}

.feature-icon {
    width: 80px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    margin: 0 auto 1.5rem;
    background: linear-gradient(45deg, var(--primary-color), var(--accent-color));
    color: white;
    font-size: 2rem;
    box-shadow: 0 10px 20px rgba(0,0,0,0.1);
}

.feature-card .card-title {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    font-weight: 700;
}

.feature-card .btn {
    border-radius: 50px;
    padding: 0.5rem 1.5rem;
    font-weight: 600;
    margin-top: 1rem;
}

/* News and Publications Sections */
.news-card {
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0,0,0,0.05);
    transition: all 0.3s ease;
    height: 100%;
}

.news-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
}

.news-card .card-img-top {
    height: 200px;
    object-fit: cover;
}

.news-card .card-body {
    padding: 1.5rem;
}

.news-card .card-title {
    font-size: 1.25rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.news-category {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    border-radius: 50px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    background-color: var(--primary-light);
    color: white;
    margin-bottom: 0.75rem;
}

.news-date {
    font-size: 0.875rem;
    color: var(--gray-600);
    margin-bottom: 0.75rem;
    display: block;
}

/* Publication Cards */
.publication-card {
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0,0,0,0.05);
    transition: all 0.3s ease;
    height: 100%;
    text-align: center;
    padding: 2rem;
}

.publication-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
}

.publication-icon {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: 1.5rem;
}

.publication-card .card-title {
    font-size: 1.25rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

/* Partners Section */
.partners-section {
    padding: 5rem 0;
    background-color: var(--gray-100);
}

.partner-logo {
    padding: 1.5rem;
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.05);
    height: 100px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.partner-logo:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.1);
}

.partner-logo img {
    max-height: 60px;
    max-width: 100%;
    filter: grayscale(100%);
    opacity: 0.7;
    transition: all 0.3s ease;
}

.partner-logo:hover img {
    filter: grayscale(0%);
    opacity: 1;
}

/* Call to Action Section */
.cta-section {
    padding: 5rem 0;
    background: linear-gradient(45deg, var(--primary-dark), var(--primary-color));
    position: relative;
    overflow: hidden;
}

.cta-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%23ffffff' fill-opacity='0.05' fill-rule='evenodd'/%3E%3C/svg%3E");
    opacity: 0.5;
}

.cta-content {
    position: relative;
    z-index: 1;
}

.cta-title {
    font-size: 2.5rem;
    font-weight: 800;
    margin-bottom: 1.5rem;
}

.cta-form {
    background: rgba(255, 255, 255, 0.1);
    padding: 0.5rem;
    border-radius: 50px;
    backdrop-filter: blur(5px);
    max-width: 600px;
    margin: 0 auto;
}

.cta-form input {
    background: transparent;
    border: none;
    color: white;
    padding: 0.75rem 1.5rem;
}

.cta-form input::placeholder {
    color: rgba(255, 255, 255, 0.7);
}

.cta-form .btn {
    border-radius: 50px;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    background-color: white;
    color: var(--primary-color);
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-subtitle {
        font-size: 1.25rem;
    }
    
    .section-title {
        font-size: 2rem;
    }
}
