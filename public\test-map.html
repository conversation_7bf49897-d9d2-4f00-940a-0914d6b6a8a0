<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Direct Map Test</title>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <style>
        body { margin: 0; padding: 20px; font-family: Arial, sans-serif; }
        #map { height: 500px; width: 100%; border: 2px solid #ccc; }
        .info { margin: 10px 0; padding: 10px; background: #f0f0f0; }
    </style>
</head>
<body>
    <h1>Direct Map Test (No Laravel)</h1>
    <div class="info">
        <p>This tests if Leaflet works without any Laravel interference.</p>
        <p id="status">Loading...</p>
    </div>
    
    <div id="map"></div>

    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const statusEl = document.getElementById('status');
            
            try {
                statusEl.textContent = 'Leaflet version: ' + L.version;
                
                // Create map
                const map = L.map('map').setView([9.0820, 8.6753], 6);
                
                // Try multiple tile providers
                const tileProviders = [
                    {
                        name: 'CartoDB',
                        url: 'https://{s}.basemaps.cartocdn.com/light_all/{z}/{x}/{y}{r}.png',
                        options: {
                            attribution: '&copy; OpenStreetMap contributors &copy; CARTO',
                            maxZoom: 19,
                            subdomains: 'abcd'
                        }
                    },
                    {
                        name: 'OpenStreetMap',
                        url: 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png',
                        options: {
                            attribution: '&copy; OpenStreetMap contributors',
                            maxZoom: 19
                        }
                    }
                ];
                
                let tileLayer;
                try {
                    tileLayer = L.tileLayer(tileProviders[0].url, tileProviders[0].options);
                } catch (e) {
                    console.log('CartoDB failed, trying OpenStreetMap...');
                    tileLayer = L.tileLayer(tileProviders[1].url, tileProviders[1].options);
                }
                
                tileLayer.addTo(map);
                
                // Add test markers
                L.marker([9.0820, 8.6753]).addTo(map)
                    .bindPopup('Nigeria Center')
                    .openPopup();
                
                // Add flood risk markers
                const floodMarkers = [
                    {lat: 9.0765, lng: 7.3986, name: 'FCT Abuja', risk: 'High'},
                    {lat: 6.6018, lng: 3.5106, name: 'Lagos', risk: 'High'},
                    {lat: 4.8156, lng: 7.0498, name: 'Port Harcourt', risk: 'High'},
                    {lat: 12.0022, lng: 8.5920, name: 'Kano', risk: 'Moderate'},
                    {lat: 7.1475, lng: 3.3619, name: 'Abeokuta', risk: 'Low'}
                ];
                
                floodMarkers.forEach(function(marker) {
                    let color;
                    switch(marker.risk) {
                        case 'High': color = 'red'; break;
                        case 'Moderate': color = 'orange'; break;
                        case 'Low': color = 'yellow'; break;
                        default: color = 'blue';
                    }
                    
                    L.circleMarker([marker.lat, marker.lng], {
                        radius: 8,
                        fillColor: color,
                        color: '#000',
                        weight: 2,
                        opacity: 1,
                        fillOpacity: 0.8
                    }).addTo(map)
                    .bindPopup(`<b>${marker.name}</b><br>Risk: ${marker.risk}`);
                });
                
                statusEl.textContent = 'Map loaded successfully! Leaflet version: ' + L.version;
                statusEl.style.color = 'green';
                
            } catch (error) {
                statusEl.textContent = 'Error: ' + error.message;
                statusEl.style.color = 'red';
                console.error('Map error:', error);
            }
        });
    </script>
</body>
</html>
