@extends('layouts.admin')

@section('title', 'Data Requests Management')

@section('content')
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
            <li class="breadcrumb-item active" aria-current="page">Data Requests Management</li>
        </ol>
    </nav>

    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom">
        <h1 class="h2">Data Requests Management</h1>
    </div>
    
    <!-- Filters -->
    <div class="card shadow-sm mb-4">
        <div class="card-body">
            <form action="{{ route('admin.data-requests.index') }}" method="GET" class="row g-3">
                <div class="col-md-3">
                    <label for="search" class="form-label">Search</label>
                    <input type="text" class="form-control" id="search" name="search" placeholder="Search by name or email" value="{{ request('search') }}">
                </div>
                <div class="col-md-3">
                    <label for="data_type" class="form-label">Data Type</label>
                    <select class="form-select" id="data_type" name="data_type">
                        <option value="">All Types</option>
                        <option value="Flood Data" {{ request('data_type') == 'Flood Data' ? 'selected' : '' }}>Flood Data</option>
                        <option value="Rainfall Data" {{ request('data_type') == 'Rainfall Data' ? 'selected' : '' }}>Rainfall Data</option>
                        <option value="Groundwater Data" {{ request('data_type') == 'Groundwater Data' ? 'selected' : '' }}>Groundwater Data</option>
                        <option value="Surface Water Data" {{ request('data_type') == 'Surface Water Data' ? 'selected' : '' }}>Surface Water Data</option>
                        <option value="Water Quality Data" {{ request('data_type') == 'Water Quality Data' ? 'selected' : '' }}>Water Quality Data</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="status" class="form-label">Status</label>
                    <select class="form-select" id="status" name="status">
                        <option value="">All Statuses</option>
                        <option value="pending" {{ request('status') == 'pending' ? 'selected' : '' }}>Pending</option>
                        <option value="approved" {{ request('status') == 'approved' ? 'selected' : '' }}>Approved</option>
                        <option value="rejected" {{ request('status') == 'rejected' ? 'selected' : '' }}>Rejected</option>
                        <option value="delivered" {{ request('status') == 'delivered' ? 'selected' : '' }}>Delivered</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="date_range" class="form-label">Date Range</label>
                    <select class="form-select" id="date_range" name="date_range">
                        <option value="">All Time</option>
                        <option value="today" {{ request('date_range') == 'today' ? 'selected' : '' }}>Today</option>
                        <option value="week" {{ request('date_range') == 'week' ? 'selected' : '' }}>This Week</option>
                        <option value="month" {{ request('date_range') == 'month' ? 'selected' : '' }}>This Month</option>
                        <option value="year" {{ request('date_range') == 'year' ? 'selected' : '' }}>This Year</option>
                    </select>
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary w-100">Filter</button>
                </div>
            </form>
        </div>
    </div>
    
    <!-- Data Requests List -->
    <div class="card shadow-sm">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover table-admin">
                    <thead>
                        <tr>
                            <th width="50">#</th>
                            <th>Name</th>
                            <th>Email</th>
                            <th>Data Type</th>
                            <th>Purpose</th>
                            <th>Date Requested</th>
                            <th>Status</th>
                            <th width="150">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- In a real implementation, this would be populated from the database -->
                        @php
                            $dummyRequests = [
                                [
                                    'id' => 1,
                                    'name' => 'John Smith',
                                    'email' => '<EMAIL>',
                                    'data_type' => 'Flood Data',
                                    'purpose' => 'Research on flood patterns in Lagos State',
                                    'created_at' => '2025-05-05',
                                    'status' => 'pending'
                                ],
                                [
                                    'id' => 2,
                                    'name' => 'Jane Doe',
                                    'email' => '<EMAIL>',
                                    'data_type' => 'Rainfall Data',
                                    'purpose' => 'Agricultural planning for the 2025 planting season',
                                    'created_at' => '2025-05-04',
                                    'status' => 'pending'
                                ],
                                [
                                    'id' => 3,
                                    'name' => 'Robert Johnson',
                                    'email' => '<EMAIL>',
                                    'data_type' => 'Groundwater Data',
                                    'purpose' => 'Environmental impact assessment for a construction project',
                                    'created_at' => '2025-05-03',
                                    'status' => 'approved'
                                ],
                                [
                                    'id' => 4,
                                    'name' => 'Emily Williams',
                                    'email' => '<EMAIL>',
                                    'data_type' => 'Surface Water Data',
                                    'purpose' => 'Academic research on water resources management',
                                    'created_at' => '2025-05-02',
                                    'status' => 'delivered'
                                ],
                                [
                                    'id' => 5,
                                    'name' => 'Michael Brown',
                                    'email' => '<EMAIL>',
                                    'data_type' => 'Water Quality Data',
                                    'purpose' => 'Water treatment plant design',
                                    'created_at' => '2025-05-01',
                                    'status' => 'rejected'
                                ],
                                [
                                    'id' => 6,
                                    'name' => 'Sarah Johnson',
                                    'email' => '<EMAIL>',
                                    'data_type' => 'Flood Data',
                                    'purpose' => 'Urban planning for flood-prone areas',
                                    'created_at' => '2025-04-30',
                                    'status' => 'pending'
                                ],
                                [
                                    'id' => 7,
                                    'name' => 'David Wilson',
                                    'email' => '<EMAIL>',
                                    'data_type' => 'Rainfall Data',
                                    'purpose' => 'Climate change impact assessment',
                                    'created_at' => '2025-04-29',
                                    'status' => 'approved'
                                ],
                                [
                                    'id' => 8,
                                    'name' => 'Jennifer Lee',
                                    'email' => '<EMAIL>',
                                    'data_type' => 'Groundwater Data',
                                    'purpose' => 'Groundwater resource assessment for a community water supply project',
                                    'created_at' => '2025-04-28',
                                    'status' => 'delivered'
                                ]
                            ];
                        @endphp
                        
                        @foreach($dummyRequests as $request)
                            <tr>
                                <td>{{ $request['id'] }}</td>
                                <td>{{ $request['name'] }}</td>
                                <td>{{ $request['email'] }}</td>
                                <td>{{ $request['data_type'] }}</td>
                                <td>{{ Str::limit($request['purpose'], 30) }}</td>
                                <td>{{ date('M d, Y', strtotime($request['created_at'])) }}</td>
                                <td>
                                    @if($request['status'] == 'pending')
                                        <span class="badge bg-warning">Pending</span>
                                    @elseif($request['status'] == 'approved')
                                        <span class="badge bg-success">Approved</span>
                                    @elseif($request['status'] == 'rejected')
                                        <span class="badge bg-danger">Rejected</span>
                                    @elseif($request['status'] == 'delivered')
                                        <span class="badge bg-info">Delivered</span>
                                    @endif
                                </td>
                                <td>
                                    <div class="btn-group">
                                        <a href="{{ route('admin.data-requests.show', $request['id']) }}" class="btn btn-sm btn-outline-primary" title="View">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        @if($request['status'] == 'pending')
                                            <button type="button" class="btn btn-sm btn-outline-success" title="Approve" data-bs-toggle="modal" data-bs-target="#approveModal{{ $request['id'] }}">
                                                <i class="fas fa-check"></i>
                                            </button>
                                            <button type="button" class="btn btn-sm btn-outline-danger" title="Reject" data-bs-toggle="modal" data-bs-target="#rejectModal{{ $request['id'] }}">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        @endif
                                    </div>
                                    
                                    <!-- Approve Modal -->
                                    <div class="modal fade" id="approveModal{{ $request['id'] }}" tabindex="-1" aria-labelledby="approveModalLabel{{ $request['id'] }}" aria-hidden="true">
                                        <div class="modal-dialog">
                                            <div class="modal-content">
                                                <div class="modal-header">
                                                    <h5 class="modal-title" id="approveModalLabel{{ $request['id'] }}">Approve Data Request</h5>
                                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                </div>
                                                <form action="{{ route('admin.data-requests.approve', $request['id']) }}" method="POST">
                                                    @csrf
                                                    <div class="modal-body">
                                                        <p>You are about to approve the data request from <strong>{{ $request['name'] }}</strong> for <strong>{{ $request['data_type'] }}</strong>.</p>
                                                        
                                                        <div class="mb-3">
                                                            <label for="notes" class="form-label">Notes (Optional)</label>
                                                            <textarea class="form-control" id="notes" name="notes" rows="3" placeholder="Add any notes or instructions for the requester"></textarea>
                                                        </div>
                                                        
                                                        <div class="mb-3">
                                                            <label for="estimated_delivery" class="form-label">Estimated Delivery Date</label>
                                                            <input type="date" class="form-control" id="estimated_delivery" name="estimated_delivery" value="{{ date('Y-m-d', strtotime('+7 days')) }}">
                                                        </div>
                                                    </div>
                                                    <div class="modal-footer">
                                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                                        <button type="submit" class="btn btn-success">Approve Request</button>
                                                    </div>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- Reject Modal -->
                                    <div class="modal fade" id="rejectModal{{ $request['id'] }}" tabindex="-1" aria-labelledby="rejectModalLabel{{ $request['id'] }}" aria-hidden="true">
                                        <div class="modal-dialog">
                                            <div class="modal-content">
                                                <div class="modal-header">
                                                    <h5 class="modal-title" id="rejectModalLabel{{ $request['id'] }}">Reject Data Request</h5>
                                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                </div>
                                                <form action="{{ route('admin.data-requests.reject', $request['id']) }}" method="POST">
                                                    @csrf
                                                    <div class="modal-body">
                                                        <p>You are about to reject the data request from <strong>{{ $request['name'] }}</strong> for <strong>{{ $request['data_type'] }}</strong>.</p>
                                                        
                                                        <div class="mb-3">
                                                            <label for="rejection_reason" class="form-label">Reason for Rejection <span class="text-danger">*</span></label>
                                                            <textarea class="form-control" id="rejection_reason" name="rejection_reason" rows="3" placeholder="Provide a reason for rejecting this request" required></textarea>
                                                        </div>
                                                    </div>
                                                    <div class="modal-footer">
                                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                                        <button type="submit" class="btn btn-danger">Reject Request</button>
                                                    </div>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            <nav aria-label="Page navigation" class="mt-4">
                <ul class="pagination justify-content-center">
                    <li class="page-item disabled">
                        <a class="page-link" href="#" tabindex="-1" aria-disabled="true">Previous</a>
                    </li>
                    <li class="page-item active"><a class="page-link" href="#">1</a></li>
                    <li class="page-item"><a class="page-link" href="#">2</a></li>
                    <li class="page-item"><a class="page-link" href="#">3</a></li>
                    <li class="page-item">
                        <a class="page-link" href="#">Next</a>
                    </li>
                </ul>
            </nav>
        </div>
    </div>
@endsection
