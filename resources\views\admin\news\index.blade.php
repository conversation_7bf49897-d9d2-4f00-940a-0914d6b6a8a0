@extends('layouts.admin')

@section('title', 'News Management')

@section('content')
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
            <li class="breadcrumb-item active" aria-current="page">News Management</li>
        </ol>
    </nav>

    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom">
        <h1 class="h2">News Management</h1>
        <div class="btn-toolbar mb-2 mb-md-0">
            <a href="{{ route('admin.news.create') }}" class="btn btn-sm btn-primary">
                <i class="fas fa-plus me-1"></i> Add News
            </a>
        </div>
    </div>
    
    <!-- Filters -->
    <div class="card shadow-sm mb-4">
        <div class="card-body">
            <form action="{{ route('admin.news.index') }}" method="GET" class="row g-3">
                <div class="col-md-4">
                    <label for="search" class="form-label">Search</label>
                    <input type="text" class="form-control" id="search" name="search" placeholder="Search by title or content" value="{{ request('search') }}">
                </div>
                <div class="col-md-3">
                    <label for="category" class="form-label">Category</label>
                    <select class="form-select" id="category" name="category">
                        <option value="">All Categories</option>
                        <option value="News" {{ request('category') == 'News' ? 'selected' : '' }}>News</option>
                        <option value="Press Release" {{ request('category') == 'Press Release' ? 'selected' : '' }}>Press Release</option>
                        <option value="Event" {{ request('category') == 'Event' ? 'selected' : '' }}>Event</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="date_range" class="form-label">Date Range</label>
                    <select class="form-select" id="date_range" name="date_range">
                        <option value="">All Time</option>
                        <option value="today" {{ request('date_range') == 'today' ? 'selected' : '' }}>Today</option>
                        <option value="week" {{ request('date_range') == 'week' ? 'selected' : '' }}>This Week</option>
                        <option value="month" {{ request('date_range') == 'month' ? 'selected' : '' }}>This Month</option>
                        <option value="year" {{ request('date_range') == 'year' ? 'selected' : '' }}>This Year</option>
                    </select>
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary w-100">Filter</button>
                </div>
            </form>
        </div>
    </div>
    
    <!-- News List -->
    <div class="card shadow-sm">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover table-admin">
                    <thead>
                        <tr>
                            <th width="50">#</th>
                            <th>Title</th>
                            <th>Category</th>
                            <th>Published Date</th>
                            <th>Featured</th>
                            <th width="150">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- In a real implementation, this would be populated from the database -->
                        @php
                            $dummyNews = [
                                [
                                    'id' => 1,
                                    'title' => '2025 Annual Flood Outlook Released',
                                    'category' => 'Press Release',
                                    'published_at' => '2025-05-05',
                                    'is_featured' => true
                                ],
                                [
                                    'id' => 2,
                                    'title' => 'NIHSA Partners with International Organizations',
                                    'category' => 'News',
                                    'published_at' => '2025-04-28',
                                    'is_featured' => true
                                ],
                                [
                                    'id' => 3,
                                    'title' => 'New Hydrological Stations Commissioned',
                                    'category' => 'News',
                                    'published_at' => '2025-04-15',
                                    'is_featured' => true
                                ],
                                [
                                    'id' => 4,
                                    'title' => 'NIHSA Conducts Flood Awareness Campaign',
                                    'category' => 'Event',
                                    'published_at' => '2025-04-10',
                                    'is_featured' => false
                                ],
                                [
                                    'id' => 5,
                                    'title' => 'NIHSA Director General Addresses UN Climate Conference',
                                    'category' => 'News',
                                    'published_at' => '2025-04-05',
                                    'is_featured' => false
                                ],
                                [
                                    'id' => 6,
                                    'title' => 'NIHSA Hosts Workshop on Flood Risk Management',
                                    'category' => 'Event',
                                    'published_at' => '2025-03-25',
                                    'is_featured' => false
                                ]
                            ];
                        @endphp
                        
                        @foreach($dummyNews as $news)
                            <tr>
                                <td>{{ $news['id'] }}</td>
                                <td>{{ $news['title'] }}</td>
                                <td><span class="badge bg-secondary">{{ $news['category'] }}</span></td>
                                <td>{{ date('M d, Y', strtotime($news['published_at'])) }}</td>
                                <td>
                                    @if($news['is_featured'])
                                        <span class="badge bg-success">Featured</span>
                                    @else
                                        <span class="badge bg-light text-dark">No</span>
                                    @endif
                                </td>
                                <td>
                                    <div class="btn-group">
                                        <a href="{{ route('news.show', $news['id']) }}" class="btn btn-sm btn-outline-primary" target="_blank" title="View">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ route('admin.news.edit', $news['id']) }}" class="btn btn-sm btn-outline-secondary" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button type="button" class="btn btn-sm btn-outline-danger" title="Delete" data-bs-toggle="modal" data-bs-target="#deleteModal{{ $news['id'] }}">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                    
                                    <!-- Delete Modal -->
                                    <div class="modal fade" id="deleteModal{{ $news['id'] }}" tabindex="-1" aria-labelledby="deleteModalLabel{{ $news['id'] }}" aria-hidden="true">
                                        <div class="modal-dialog">
                                            <div class="modal-content">
                                                <div class="modal-header">
                                                    <h5 class="modal-title" id="deleteModalLabel{{ $news['id'] }}">Confirm Delete</h5>
                                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                </div>
                                                <div class="modal-body">
                                                    Are you sure you want to delete the news article "{{ $news['title'] }}"? This action cannot be undone.
                                                </div>
                                                <div class="modal-footer">
                                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                                    <form action="{{ route('admin.news.destroy', $news['id']) }}" method="POST" class="d-inline">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit" class="btn btn-danger">Delete</button>
                                                    </form>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            <nav aria-label="Page navigation" class="mt-4">
                <ul class="pagination justify-content-center">
                    <li class="page-item disabled">
                        <a class="page-link" href="#" tabindex="-1" aria-disabled="true">Previous</a>
                    </li>
                    <li class="page-item active"><a class="page-link" href="#">1</a></li>
                    <li class="page-item"><a class="page-link" href="#">2</a></li>
                    <li class="page-item"><a class="page-link" href="#">3</a></li>
                    <li class="page-item">
                        <a class="page-link" href="#">Next</a>
                    </li>
                </ul>
            </nav>
        </div>
    </div>
@endsection
