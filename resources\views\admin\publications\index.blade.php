@extends('layouts.admin')

@section('title', 'Publications Management')

@section('content')
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
            <li class="breadcrumb-item active" aria-current="page">Publications Management</li>
        </ol>
    </nav>

    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom">
        <h1 class="h2">Publications Management</h1>
        <div class="btn-toolbar mb-2 mb-md-0">
            <a href="{{ route('admin.publications.create') }}" class="btn btn-sm btn-primary">
                <i class="fas fa-plus me-1"></i> Add Publication
            </a>
        </div>
    </div>
    
    <!-- Filters -->
    <div class="card shadow-sm mb-4">
        <div class="card-body">
            <form action="{{ route('admin.publications.index') }}" method="GET" class="row g-3">
                <div class="col-md-3">
                    <label for="search" class="form-label">Search</label>
                    <input type="text" class="form-control" id="search" name="search" placeholder="Search by title" value="{{ request('search') }}">
                </div>
                <div class="col-md-3">
                    <label for="type" class="form-label">Type</label>
                    <select class="form-select" id="type" name="type">
                        <option value="">All Types</option>
                        <option value="AFO" {{ request('type') == 'AFO' ? 'selected' : '' }}>Annual Flood Outlook</option>
                        <option value="Bulletin" {{ request('type') == 'Bulletin' ? 'selected' : '' }}>Bulletin</option>
                        <option value="Report" {{ request('type') == 'Report' ? 'selected' : '' }}>Report</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="year" class="form-label">Year</label>
                    <select class="form-select" id="year" name="year">
                        <option value="">All Years</option>
                        <option value="2025" {{ request('year') == '2025' ? 'selected' : '' }}>2025</option>
                        <option value="2024" {{ request('year') == '2024' ? 'selected' : '' }}>2024</option>
                        <option value="2023" {{ request('year') == '2023' ? 'selected' : '' }}>2023</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="featured" class="form-label">Featured</label>
                    <select class="form-select" id="featured" name="featured">
                        <option value="">All</option>
                        <option value="1" {{ request('featured') == '1' ? 'selected' : '' }}>Featured</option>
                        <option value="0" {{ request('featured') == '0' ? 'selected' : '' }}>Not Featured</option>
                    </select>
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary w-100">Filter</button>
                </div>
            </form>
        </div>
    </div>
    
    <!-- Publications List -->
    <div class="card shadow-sm">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover table-admin">
                    <thead>
                        <tr>
                            <th width="50">#</th>
                            <th>Title</th>
                            <th>Type</th>
                            <th>Year</th>
                            <th>Publication Date</th>
                            <th>Featured</th>
                            <th width="150">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- In a real implementation, this would be populated from the database -->
                        @php
                            $dummyPublications = [
                                [
                                    'id' => 1,
                                    'title' => '2025 Annual Flood Outlook',
                                    'description' => 'Comprehensive flood predictions for the 2025 rainy season.',
                                    'type' => 'AFO',
                                    'year' => 2025,
                                    'publication_date' => '2025-04-01',
                                    'file_path' => '#',
                                    'is_featured' => true
                                ],
                                [
                                    'id' => 2,
                                    'title' => 'Flood Mitigation & Adaptation Measures',
                                    'description' => 'Guidelines for flood mitigation and adaptation strategies.',
                                    'type' => 'Report',
                                    'year' => 2025,
                                    'publication_date' => '2025-04-01',
                                    'file_path' => '#',
                                    'is_featured' => true
                                ],
                                [
                                    'id' => 3,
                                    'title' => 'Flood Risk Communication',
                                    'description' => 'Strategies for effective flood risk communication.',
                                    'type' => 'Report',
                                    'year' => 2025,
                                    'publication_date' => '2025-04-01',
                                    'file_path' => '#',
                                    'is_featured' => true
                                ],
                                [
                                    'id' => 4,
                                    'title' => '2024 Annual Flood Outlook',
                                    'description' => 'Comprehensive flood predictions for the 2024 rainy season.',
                                    'type' => 'AFO',
                                    'year' => 2024,
                                    'publication_date' => '2024-04-01',
                                    'file_path' => '#',
                                    'is_featured' => false
                                ],
                                [
                                    'id' => 5,
                                    'title' => '2023 Annual Flood Outlook',
                                    'description' => 'Comprehensive flood predictions for the 2023 rainy season.',
                                    'type' => 'AFO',
                                    'year' => 2023,
                                    'publication_date' => '2023-04-01',
                                    'file_path' => '#',
                                    'is_featured' => false
                                ],
                                [
                                    'id' => 6,
                                    'title' => 'Flood and Drought Bulletin - January 2025',
                                    'description' => 'Monthly bulletin on flood and drought conditions across Nigeria.',
                                    'type' => 'Bulletin',
                                    'year' => 2025,
                                    'publication_date' => '2025-01-31',
                                    'file_path' => '#',
                                    'is_featured' => false
                                ],
                                [
                                    'id' => 7,
                                    'title' => 'Flood and Drought Bulletin - February 2025',
                                    'description' => 'Monthly bulletin on flood and drought conditions across Nigeria.',
                                    'type' => 'Bulletin',
                                    'year' => 2025,
                                    'publication_date' => '2025-02-28',
                                    'file_path' => '#',
                                    'is_featured' => false
                                ],
                                [
                                    'id' => 8,
                                    'title' => 'Flood and Drought Bulletin - March 2025',
                                    'description' => 'Monthly bulletin on flood and drought conditions across Nigeria.',
                                    'type' => 'Bulletin',
                                    'year' => 2025,
                                    'publication_date' => '2025-03-31',
                                    'file_path' => '#',
                                    'is_featured' => false
                                ]
                            ];
                        @endphp
                        
                        @foreach($dummyPublications as $publication)
                            <tr>
                                <td>{{ $publication['id'] }}</td>
                                <td>{{ $publication['title'] }}</td>
                                <td><span class="badge bg-secondary">{{ $publication['type'] }}</span></td>
                                <td>{{ $publication['year'] }}</td>
                                <td>{{ date('M d, Y', strtotime($publication['publication_date'])) }}</td>
                                <td>
                                    @if($publication['is_featured'])
                                        <span class="badge bg-success">Featured</span>
                                    @else
                                        <span class="badge bg-light text-dark">No</span>
                                    @endif
                                </td>
                                <td>
                                    <div class="btn-group">
                                        <a href="{{ route('publications.show', $publication['id']) }}" class="btn btn-sm btn-outline-primary" target="_blank" title="View">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ route('admin.publications.edit', $publication['id']) }}" class="btn btn-sm btn-outline-secondary" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button type="button" class="btn btn-sm btn-outline-danger" title="Delete" data-bs-toggle="modal" data-bs-target="#deleteModal{{ $publication['id'] }}">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                    
                                    <!-- Delete Modal -->
                                    <div class="modal fade" id="deleteModal{{ $publication['id'] }}" tabindex="-1" aria-labelledby="deleteModalLabel{{ $publication['id'] }}" aria-hidden="true">
                                        <div class="modal-dialog">
                                            <div class="modal-content">
                                                <div class="modal-header">
                                                    <h5 class="modal-title" id="deleteModalLabel{{ $publication['id'] }}">Confirm Delete</h5>
                                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                </div>
                                                <div class="modal-body">
                                                    Are you sure you want to delete the publication "{{ $publication['title'] }}"? This action cannot be undone.
                                                </div>
                                                <div class="modal-footer">
                                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                                    <form action="{{ route('admin.publications.destroy', $publication['id']) }}" method="POST" class="d-inline">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit" class="btn btn-danger">Delete</button>
                                                    </form>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            <nav aria-label="Page navigation" class="mt-4">
                <ul class="pagination justify-content-center">
                    <li class="page-item disabled">
                        <a class="page-link" href="#" tabindex="-1" aria-disabled="true">Previous</a>
                    </li>
                    <li class="page-item active"><a class="page-link" href="#">1</a></li>
                    <li class="page-item"><a class="page-link" href="#">2</a></li>
                    <li class="page-item"><a class="page-link" href="#">3</a></li>
                    <li class="page-item">
                        <a class="page-link" href="#">Next</a>
                    </li>
                </ul>
            </nav>
        </div>
    </div>
@endsection
