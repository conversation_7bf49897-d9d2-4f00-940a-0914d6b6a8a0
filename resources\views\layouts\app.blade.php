<!doctype html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="Nigeria Hydrological Services Agency - Water Resources Data for Sustainable Development">
    <meta name="keywords" content="NIHSA, hydrology, water resources, flood forecast, Nigeria">

    <!-- CSRF Token -->
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>@yield('title', config('app.name', 'NIHSA'))</title>

    <!-- Fonts -->
    <link rel="dns-prefetch" href="//fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=Nunito:400,600,700|Roboto:400,500,700" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Animate On Scroll Library -->
    <link rel="stylesheet" href="https://unpkg.com/aos@next/dist/aos.css" />

    <!-- Scripts -->
    @vite(['resources/sass/app.scss', 'resources/js/app.js'])

    <!-- Fix for shaking issue -->
    <link rel="stylesheet" href="{{ asset('css/fix-shaking.css') }}">

    <!-- Custom NIHSA Redesign Styles -->
    <link rel="stylesheet" href="{{ asset('css/nihsa-redesign.css') }}">

    <!-- Additional Styles -->
    <style>
        :root {
            --primary-color: #0056b3;
            --secondary-color: #28a745;
            --accent-color: #17a2b8;
            --dark-color: #343a40;
            --light-color: #f8f9fa;
        }

        body {
            font-family: 'Roboto', sans-serif;
            color: #333;
            background-color: #f5f5f5;
        }

        .navbar-brand img {
            max-height: 50px;
        }

        .top-bar {
            background-color: var(--primary-color);
            color: white;
            padding: 5px 0;
            font-size: 0.9rem;
        }

        .top-bar a {
            color: white;
            margin-right: 15px;
        }

        .main-navbar {
            background-color: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .nav-link {
            font-weight: 500;
            color: var(--dark-color) !important;
            padding: 0.5rem 1rem;
            transition: color 0.3s;
        }

        .nav-link:hover {
            color: var(--primary-color) !important;
        }

        .dropdown-menu {
            border: none;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .footer {
            background-color: var(--dark-color);
            color: white;
            padding: 3rem 0;
        }

        .footer h5 {
            color: var(--light-color);
            font-weight: 600;
            margin-bottom: 1.5rem;
        }

        .footer-links {
            list-style: none;
            padding-left: 0;
        }

        .footer-links li {
            margin-bottom: 0.5rem;
        }

        .footer-links a {
            color: rgba(255,255,255,0.7);
            text-decoration: none;
            transition: color 0.3s;
        }

        .footer-links a:hover {
            color: white;
        }

        .copyright {
            background-color: rgba(0,0,0,0.2);
            padding: 1rem 0;
            color: rgba(255,255,255,0.7);
        }

        .social-icons a {
            color: white;
            margin-right: 15px;
            font-size: 1.2rem;
        }

        /* Custom button styles */
        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .btn-secondary {
            background-color: var(--secondary-color);
            border-color: var(--secondary-color);
        }

        /* Hero section */
        .hero-section {
            background-size: cover;
            background-position: center;
            color: white;
            padding: 5rem 0;
            position: relative;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.5);
        }

        .hero-content {
            position: relative;
            z-index: 1;
        }

        /* Card styles */
        .card {
            border: none;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: transform 0.3s;
        }

        .card:hover {
            transform: translateY(-5px);
        }
    </style>

    @yield('styles')
</head>
<body>
    <div id="app">
        <!-- Top Bar -->
        <div class="top-bar d-none d-md-block">
            <div class="container">
                <div class="row">
                    <div class="col-md-6">
                        <a href="mailto:<EMAIL>"><i class="fas fa-envelope me-1"></i> <EMAIL></a>
                        <a href="tel:+2348012345678"><i class="fas fa-phone me-1"></i> +234 ************</a>
                    </div>
                    <div class="col-md-6 text-end">
                        <a href="#"><i class="fas fa-search me-1"></i> Search</a>
                        <a href="{{ route('login') }}"><i class="fas fa-user me-1"></i> Login</a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Navigation -->
        <nav class="navbar navbar-expand-md main-navbar">
            <div class="container">
                <a class="navbar-brand" href="{{ url('/') }}">
                    <img src="{{ asset('images/nihsa-logo.png') }}" alt="{{ config('app.name', 'NIHSA') }}" onerror="this.src='{{ asset('images/nihsa-logo-placeholder.svg') }}'">
                </a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarSupportedContent" aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="{{ __('Toggle navigation') }}">
                    <span class="navbar-toggler-icon"></span>
                </button>

                <div class="collapse navbar-collapse" id="navbarSupportedContent">
                    <!-- Main Navigation Links -->
                    <ul class="navbar-nav me-auto">
                        <li class="nav-item">
                            <a class="nav-link {{ request()->routeIs('home') ? 'active' : '' }}" href="{{ route('home') }}">Home</a>
                        </li>

                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle {{ request()->routeIs('about*') ? 'active' : '' }}" href="#" id="aboutDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                Who We Are
                            </a>
                            <ul class="dropdown-menu" aria-labelledby="aboutDropdown">
                                <li><a class="dropdown-item" href="{{ route('about') }}">About Us</a></li>
                                <li><a class="dropdown-item" href="{{ route('about.functions') }}">Functions of The Agency</a></li>
                                <li><a class="dropdown-item" href="{{ route('about.management') }}">Management</a></li>
                                <li><a class="dropdown-item" href="{{ route('about.structure') }}">Organisational Structure</a></li>
                                <li><a class="dropdown-item" href="{{ route('about.offices') }}">Area and Field Offices</a></li>
                                <li><a class="dropdown-item" href="{{ route('about.history') }}">History</a></li>
                            </ul>
                        </li>

                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle {{ request()->routeIs('products.*') || request()->routeIs('services.*') || request()->routeIs('data-request.*') ? 'active' : '' }}" href="#" id="productsDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                Products and Services
                            </a>
                            <ul class="dropdown-menu" aria-labelledby="productsDropdown">
                                <li><a class="dropdown-item {{ request()->routeIs('products.*') ? 'active' : '' }}" href="{{ route('products.index') }}">Products</a></li>
                                <li><a class="dropdown-item {{ request()->routeIs('services.*') ? 'active' : '' }}" href="{{ route('services.index') }}">Services</a></li>
                                <li><a class="dropdown-item {{ request()->routeIs('data-request.*') ? 'active' : '' }}" href="{{ route('data-request.create') }}">Data Request</a></li>
                            </ul>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link {{ request()->routeIs('publications.index') ? 'active' : '' }}" href="{{ route('publications.index') }}">Publications</a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link {{ request()->routeIs('news.index') ? 'active' : '' }}" href="{{ route('news.index') }}">Media</a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link {{ request()->routeIs('flood-forecast-dashboard') ? 'active' : '' }}" href="{{ route('flood-forecast-dashboard') }}">Flood Prediction</a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link {{ request()->routeIs('contact.index') ? 'active' : '' }}" href="{{ route('contact.index') }}">Get In Touch</a>
                        </li>
                    </ul>

                    <!-- Right Side Of Navbar -->
                    <ul class="navbar-nav ms-auto">
                        <!-- Authentication Links -->
                        @guest
                            @if (Route::has('login'))
                                <li class="nav-item d-md-none">
                                    <a class="nav-link" href="{{ route('login') }}">{{ __('Login') }}</a>
                                </li>
                            @endif

                            @if (Route::has('register'))
                                <li class="nav-item d-md-none">
                                    <a class="nav-link" href="{{ route('register') }}">{{ __('Register') }}</a>
                                </li>
                            @endif
                        @else
                            <li class="nav-item dropdown">
                                <a id="navbarDropdown" class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false" v-pre>
                                    {{ Auth::user()->name }}
                                </a>

                                <div class="dropdown-menu dropdown-menu-end" aria-labelledby="navbarDropdown">
                                    <a class="dropdown-item" href="{{ route('admin.dashboard') }}">
                                        <i class="fas fa-tachometer-alt me-2"></i> Dashboard
                                    </a>

                                    <div class="dropdown-divider"></div>

                                    <a class="dropdown-item" href="{{ route('logout') }}"
                                       onclick="event.preventDefault();
                                                     document.getElementById('logout-form').submit();">
                                        <i class="fas fa-sign-out-alt me-2"></i> {{ __('Logout') }}
                                    </a>

                                    <form id="logout-form" action="{{ route('logout') }}" method="POST" class="d-none">
                                        @csrf
                                    </form>
                                </div>
                            </li>
                        @endguest
                    </ul>
                </div>
            </div>
        </nav>

        <!-- Flash Messages -->
        @if(session('success'))
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                {{ session('success') }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        @endif

        @if(session('error'))
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                {{ session('error') }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        @endif

        <!-- Main Content -->
        <main>
            @yield('content')
        </main>

        <!-- Footer -->
        <footer class="footer">
            <div class="container">
                <div class="row">
                    <div class="col-md-4 mb-4 mb-md-0">
                        <h5>Nigeria Hydrological Services Agency</h5>
                        <p class="mb-3">Water Resources Data for Sustainable Development</p>
                        <p class="mb-3">
                            <i class="fas fa-map-marker-alt me-2"></i> Plot 222, Foundation Plaza,<br>
                            Shettima Ali Monguno Crescent,<br>
                            Utako, Abuja, Nigeria
                        </p>
                        <p class="mb-3">
                            <i class="fas fa-phone me-2"></i> +234 ************
                        </p>
                        <p>
                            <i class="fas fa-envelope me-2"></i> <EMAIL>
                        </p>
                    </div>

                    <div class="col-md-2 mb-4 mb-md-0">
                        <h5>Quick Links</h5>
                        <ul class="footer-links">
                            <li><a href="{{ route('home') }}">Home</a></li>
                            <li><a href="{{ route('about') }}">About Us</a></li>
                            <li><a href="{{ route('publications.index') }}">Publications</a></li>
                            <li><a href="{{ route('news.index') }}">Media</a></li>
                            <li><a href="{{ route('flood-forecast-dashboard') }}">Flood Prediction</a></li>
                            <li><a href="{{ route('contact.index') }}">Contact Us</a></li>
                        </ul>
                    </div>

                    <div class="col-md-3 mb-4 mb-md-0">
                        <h5>Resources</h5>
                        <ul class="footer-links">
                            <li><a href="#">Annual Flood Outlook</a></li>
                            <li><a href="#">Flood and Drought Bulletins</a></li>
                            <li><a href="#">Hydrological Data</a></li>
                            <li><a href="{{ route('data-request.create') }}">Request Data</a></li>
                            <li><a href="#">FAQ</a></li>
                        </ul>
                    </div>

                    <div class="col-md-3">
                        <h5>Connect With Us</h5>
                        <div class="social-icons mb-3">
                            <a href="#"><i class="fab fa-facebook-f"></i></a>
                            <a href="#"><i class="fab fa-twitter"></i></a>
                            <a href="#"><i class="fab fa-linkedin-in"></i></a>
                            <a href="#"><i class="fab fa-youtube"></i></a>
                        </div>
                        <p>Subscribe to our newsletter for updates</p>
                        <form>
                            <div class="input-group mb-3">
                                <input type="email" class="form-control" placeholder="Your Email" aria-label="Your Email">
                                <button class="btn btn-primary" type="button">Subscribe</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <div class="copyright mt-4">
                <div class="container">
                    <div class="row">
                        <div class="col-md-6">
                            <p class="mb-md-0">Copyright © {{ date('Y') }} NIHSA | Powered by NIHSA</p>
                        </div>
                        <div class="col-md-6 text-md-end">
                            <a href="#" class="text-white-50 me-3">Privacy Policy</a>
                            <a href="#" class="text-white-50">Terms of Use</a>
                        </div>
                    </div>
                </div>
            </div>
        </footer>
    </div>

    <!-- Animate On Scroll Library -->
    <script src="https://unpkg.com/aos@next/dist/aos.js"></script>

    <!-- Custom NIHSA Redesign Scripts -->
    <script src="{{ asset('js/nihsa-redesign.js') }}"></script>

    <!-- Back to Top Button -->
    <a href="#" class="back-to-top">
        <i class="fas fa-arrow-up"></i>
    </a>

    @yield('scripts')
</body>
</html>
