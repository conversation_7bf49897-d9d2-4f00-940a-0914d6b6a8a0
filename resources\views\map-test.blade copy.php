<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Map Test</title>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <style>
        #map {
            height: 500px;
            width: 100%;
            border: 2px solid #ccc;
        }
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        .info {
            margin: 20px 0;
            padding: 10px;
            background: #f0f0f0;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <h1>Map Test Page</h1>
    <div class="info">
        <p>This is a simple test to check if Leaflet maps are working.</p>
        <p id="status">Loading...</p>
    </div>
    
    <div id="map"></div>

    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const statusEl = document.getElementById('status');
            
            try {
                statusEl.textContent = 'Leaflet version: ' + L.version;
                
                // Create map
                const map = L.map('map').setView([9.0820, 8.6753], 6);
                
                // Add tile layer
                L.tileLayer('https://{s}.basemaps.cartocdn.com/light_all/{z}/{x}/{y}{r}.png', {
                    attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors &copy; <a href="https://carto.com/attributions">CARTO</a>',
                    maxZoom: 19,
                    subdomains: 'abcd'
                }).addTo(map);
                
                // Add a test marker
                L.marker([9.0820, 8.6753]).addTo(map)
                    .bindPopup('Nigeria Center')
                    .openPopup();
                
                // Add some test markers for flood data
                const testMarkers = [
                    {lat: 9.0765, lng: 7.3986, name: 'FCT Abuja', risk: 'High'},
                    {lat: 6.6018, lng: 3.5106, name: 'Lagos', risk: 'High'},
                    {lat: 4.8156, lng: 7.0498, name: 'Port Harcourt', risk: 'High'}
                ];
                
                testMarkers.forEach(function(marker) {
                    const color = marker.risk === 'High' ? 'red' : 'orange';
                    L.circleMarker([marker.lat, marker.lng], {
                        radius: 8,
                        fillColor: color,
                        color: '#000',
                        weight: 2,
                        opacity: 1,
                        fillOpacity: 0.8
                    }).addTo(map)
                    .bindPopup(`<b>${marker.name}</b><br>Risk: ${marker.risk}`);
                });
                
                statusEl.textContent = 'Map loaded successfully! Leaflet version: ' + L.version;
                statusEl.style.color = 'green';
                
            } catch (error) {
                statusEl.textContent = 'Error: ' + error.message;
                statusEl.style.color = 'red';
                console.error('Map error:', error);
            }
        });
    </script>
</body>
</html>
