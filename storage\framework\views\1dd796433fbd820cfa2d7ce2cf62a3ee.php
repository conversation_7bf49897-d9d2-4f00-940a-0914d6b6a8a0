<?php $__env->startSection('title', 'Flood Forecast Dashboard - Nigeria Hydrological Services Agency'); ?>

<?php $__env->startSection('styles'); ?>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY=" crossorigin="" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
    <style>
        /* NIHSA Dashboard Styles - Matching Official Design */
        :root {
            --nihsa-primary: #0056b3;
            --nihsa-secondary: #28a745;
            --nihsa-accent: #17a2b8;
            --nihsa-danger: #dc3545;
            --nihsa-warning: #ffc107;
            --nihsa-dark: #343a40;
            --nihsa-light: #f8f9fa;
        }

        .dashboard-hero {
            background: linear-gradient(135deg, var(--nihsa-primary) 0%, var(--nihsa-accent) 100%);
            color: white;
            padding: 3rem 0;
            margin-bottom: 2rem;
        }

        .dashboard-hero h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
        }

        .dashboard-hero .lead {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        #map {
            height: 600px !important;
            width: 100% !important;
            border-radius: 8px;
            border: 1px solid #dee2e6;
            position: relative;
            z-index: 1;
            background-color: #f0f0f0;
        }

        .dashboard-card {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            transition: all 0.3s ease;
            margin-bottom: 1.5rem;
        }

        .dashboard-card:hover {
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
            transform: translateY(-2px);
        }

        .dashboard-card .card-header {
            background: var(--nihsa-light);
            border-bottom: 1px solid #dee2e6;
            font-weight: 600;
            padding: 1rem 1.25rem;
        }

        .dashboard-card .card-header.bg-primary {
            background: var(--nihsa-primary) !important;
            color: white;
            border-bottom: none;
        }

        .dashboard-card .card-header.bg-info {
            background: var(--nihsa-accent) !important;
            color: white;
            border-bottom: none;
        }

        .dashboard-card .card-header.bg-success {
            background: var(--nihsa-secondary) !important;
            color: white;
            border-bottom: none;
        }

        .dashboard-card .card-header.bg-warning {
            background: var(--nihsa-warning) !important;
            color: var(--nihsa-dark);
            border-bottom: none;
        }

        .dashboard-card .card-header.bg-secondary {
            background: var(--nihsa-dark) !important;
            color: white;
            border-bottom: none;
        }

        .dashboard-card .card-header.bg-dark {
            background: var(--nihsa-dark) !important;
            color: white;
            border-bottom: none;
        }

        .legend {
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 0 15px rgba(0,0,0,0.2);
            position: absolute;
            bottom: 20px;
            right: 20px;
            z-index: 1000;
            min-width: 200px;
            border: 1px solid #dee2e6;
        }

        .legend-item {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
            font-size: 0.9rem;
        }

        .legend-color {
            width: 18px;
            height: 18px;
            margin-right: 10px;
            border-radius: 50%;
            border: 2px solid #333;
        }

        .stats-card {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            position: relative;
            overflow: hidden;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            transition: all 0.3s ease;
        }

        .stats-card:hover {
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
            transform: translateY(-2px);
        }

        .stats-card::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 60px;
            height: 60px;
            background: rgba(0, 86, 179, 0.1);
            border-radius: 50%;
            transform: translate(20px, -20px);
        }

        .stats-number {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            color: var(--nihsa-primary);
        }

        .stats-label {
            font-size: 0.9rem;
            color: var(--nihsa-dark);
            font-weight: 500;
        }

        .risk-high .stats-number { color: var(--nihsa-danger); }
        .risk-high::before { background: rgba(220, 53, 69, 0.1); }

        .risk-moderate .stats-number { color: var(--nihsa-warning); }
        .risk-moderate::before { background: rgba(255, 193, 7, 0.1); }

        .risk-low .stats-number { color: var(--nihsa-secondary); }
        .risk-low::before { background: rgba(40, 167, 69, 0.1); }

        .risk-total .stats-number { color: var(--nihsa-primary); }
        .risk-total::before { background: rgba(0, 86, 179, 0.1); }

        .sensor-status {
            padding: 4px 8px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .status-normal { background: #d4edda; color: #155724; }
        .status-warning { background: #fff3cd; color: #856404; }
        .status-critical { background: #f8d7da; color: #721c24; }

        .alert-card {
            border-left: 4px solid;
            border-radius: 0 8px 8px 0;
        }

        .alert-high { border-left-color: var(--nihsa-danger); }
        .alert-moderate { border-left-color: var(--nihsa-warning); }
        .alert-low { border-left-color: var(--nihsa-secondary); }

        .forecast-day {
            text-align: center;
            padding: 1rem;
            border-radius: 8px;
            background: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 10px;
            border: 1px solid #dee2e6;
        }

        .forecast-icon {
            font-size: 2rem;
            margin-bottom: 10px;
        }

        .time-slider {
            width: 100%;
            margin: 20px 0;
        }

        .export-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .data-table {
            font-size: 0.9rem;
        }

        .data-table th {
            background: var(--nihsa-light);
            border-top: none;
            font-weight: 600;
            color: var(--nihsa-dark);
        }

        .progress-thin {
            height: 4px;
        }

        .map-controls {
            position: absolute;
            top: 10px;
            left: 10px;
            z-index: 1000;
            background: white;
            padding: 10px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
            border: 1px solid #dee2e6;
        }

        .basemap-selector {
            margin-bottom: 10px;
        }

        .overlay-controls {
            margin-top: 10px;
        }

        .overlay-controls label {
            display: block;
            margin-bottom: 5px;
            font-size: 0.9rem;
        }

        /* Button Styles */
        .btn-primary {
            background-color: var(--nihsa-primary);
            border-color: var(--nihsa-primary);
        }

        .btn-primary:hover {
            background-color: #004494;
            border-color: #004494;
        }

        .btn-success {
            background-color: var(--nihsa-secondary);
            border-color: var(--nihsa-secondary);
        }

        .btn-info {
            background-color: var(--nihsa-accent);
            border-color: var(--nihsa-accent);
        }

        @media (max-width: 768px) {
            .dashboard-hero h1 {
                font-size: 2rem;
            }

            .legend {
                position: relative;
                bottom: auto;
                right: auto;
                margin-top: 20px;
            }

            .export-buttons {
                justify-content: center;
            }

            .stats-number {
                font-size: 2rem;
            }
        }
    </style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <!-- Hero Section -->
    <section class="dashboard-hero">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h1><i class="fas fa-chart-line me-3"></i>Flood Forecast Dashboard</h1>
                    <p class="lead mb-4">Real-time flood risk monitoring and forecasting system for Nigeria. Access comprehensive hydrological data, risk assessments, and early warning systems to support disaster preparedness and water resource management.</p>

                    <div class="d-flex flex-wrap gap-2">
                        <a href="#statistics" class="btn btn-light">
                            <i class="fas fa-chart-bar me-2"></i>View Statistics
                        </a>
                        <a href="#map-section" class="btn btn-outline-light">
                            <i class="fas fa-map me-2"></i>Interactive Map
                        </a>
                    </div>
                </div>
                <div class="col-lg-4 text-lg-end mt-4 mt-lg-0">
                    <div class="export-buttons">
                        <button class="btn btn-light btn-sm" onclick="exportData('csv')" title="Export as CSV">
                            <i class="fas fa-file-csv me-1"></i> CSV
                        </button>
                        <button class="btn btn-light btn-sm" onclick="exportData('geojson')" title="Export as GeoJSON">
                            <i class="fas fa-map me-1"></i> GeoJSON
                        </button>
                        <button class="btn btn-light btn-sm" onclick="exportData('pdf')" title="Export as PDF">
                            <i class="fas fa-file-pdf me-1"></i> PDF
                        </button>
                        <button class="btn btn-outline-light btn-sm" onclick="shareUrl()" title="Share Dashboard">
                            <i class="fas fa-share me-1"></i> Share
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Statistics Cards -->
    <section id="statistics" class="py-5 bg-light">
        <div class="container">
            <div class="row mb-4">
                <div class="col-12 text-center">
                    <h2 class="mb-3">Current Flood Risk Overview</h2>
                    <p class="text-muted">Real-time statistics showing flood risk distribution across Nigerian communities</p>
                </div>
            </div>
            <div class="row g-4">
                <div class="col-lg-3 col-md-6">
                    <div class="stats-card risk-total">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <div class="stats-number"><?php echo e($statistics['total_communities']); ?></div>
                                <div class="stats-label">Total Communities</div>
                            </div>
                            <div class="text-primary">
                                <i class="fas fa-home fa-2x opacity-50"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="stats-card risk-high">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <div class="stats-number"><?php echo e($statistics['high_risk']); ?></div>
                                <div class="stats-label">High Risk Areas</div>
                            </div>
                            <div class="text-danger">
                                <i class="fas fa-exclamation-triangle fa-2x opacity-50"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="stats-card risk-moderate">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <div class="stats-number"><?php echo e($statistics['moderate_risk']); ?></div>
                                <div class="stats-label">Moderate Risk Areas</div>
                            </div>
                            <div class="text-warning">
                                <i class="fas fa-exclamation-circle fa-2x opacity-50"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="stats-card risk-low">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <div class="stats-number"><?php echo e($statistics['low_risk']); ?></div>
                                <div class="stats-label">Low Risk Areas</div>
                            </div>
                            <div class="text-success">
                                <i class="fas fa-check-circle fa-2x opacity-50"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Main Dashboard -->
    <section id="map-section" class="py-5">
        <div class="container-fluid">
            <div class="row mb-4">
                <div class="col-12 text-center">
                    <h2 class="mb-3">Interactive Flood Risk Dashboard</h2>
                    <p class="text-muted">Comprehensive monitoring and analysis tools for flood risk assessment</p>
                </div>
            </div>
            <div class="row">
                <!-- Filters and Controls -->
                <div class="col-lg-3 mb-4">
                    <div class="dashboard-card">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0"><i class="fas fa-filter me-2"></i>Filter Controls</h5>
                        </div>
                        <div class="card-body">
                            <form id="filterForm">
                                <div class="mb-3">
                                    <label for="year" class="form-label">Year</label>
                                    <select class="form-select" id="year" name="year">
                                        <?php $__currentLoopData = $availableYears; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $yearOption): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($yearOption); ?>" <?php echo e($year == $yearOption ? 'selected' : ''); ?>><?php echo e($yearOption); ?></option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="period" class="form-label">Period</label>
                                    <select class="form-select" id="period" name="period">
                                        <option value="all" <?php echo e($period == 'all' ? 'selected' : ''); ?>>All Periods</option>
                                        <?php $__currentLoopData = $periods; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($key); ?>" <?php echo e($period == $key ? 'selected' : ''); ?>><?php echo e($label); ?></option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="state" class="form-label">State</label>
                                    <select class="form-select" id="state" name="state">
                                        <option value="all" <?php echo e($state == 'all' ? 'selected' : ''); ?>>All States</option>
                                        <?php $__currentLoopData = $nigerianStates; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $stateOption): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($stateOption); ?>" <?php echo e($state == $stateOption ? 'selected' : ''); ?>><?php echo e($stateOption); ?></option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="risk_level" class="form-label">Risk Level</label>
                                    <select class="form-select" id="risk_level" name="risk_level">
                                        <option value="all" <?php echo e($riskLevel == 'all' ? 'selected' : ''); ?>>All Risk Levels</option>
                                        <?php $__currentLoopData = $riskLevels; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $level): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($level); ?>" <?php echo e($riskLevel == $level ? 'selected' : ''); ?>><?php echo e($level); ?></option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="flood_type" class="form-label">Flood Type</label>
                                    <select class="form-select" id="flood_type" name="flood_type">
                                        <option value="all" <?php echo e($floodType == 'all' ? 'selected' : ''); ?>>All Types</option>
                                        <?php $__currentLoopData = $floodTypes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $type): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($type); ?>" <?php echo e($floodType == $type ? 'selected' : ''); ?>><?php echo e($type); ?></option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="admin_level" class="form-label">Administrative Level</label>
                                    <select class="form-select" id="admin_level" name="admin_level">
                                        <option value="all" <?php echo e($adminLevel == 'all' ? 'selected' : ''); ?>>All Levels</option>
                                        <?php $__currentLoopData = $adminLevels; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $level): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($level); ?>" <?php echo e($adminLevel == $level ? 'selected' : ''); ?>><?php echo e($level); ?></option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                </div>
                                
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-search me-2"></i>Apply Filters
                                </button>
                            </form>
                        </div>
                    </div>

                    <!-- Time Slider & Calendar Widget -->
                    <div class="dashboard-card mb-4">
                        <div class="card-header bg-info text-white">
                            <h5 class="mb-0"><i class="fas fa-calendar me-2"></i>Time Controls</h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label for="forecast_date" class="form-label">Select Forecast Date</label>
                                <input type="date" class="form-control" id="forecast_date" name="forecast_date" value="<?php echo e(date('Y-m-d')); ?>">
                            </div>
                            <div class="mb-3">
                                <label for="time_range" class="form-label">Time Range</label>
                                <input type="range" class="form-range time-slider" id="time_range" min="0" max="23" value="12">
                                <div class="d-flex justify-content-between">
                                    <small>00:00</small>
                                    <small id="current_time">12:00</small>
                                    <small>23:00</small>
                                </div>
                            </div>
                            <button class="btn btn-info w-100" onclick="animateTimeProgression()">
                                <i class="fas fa-play me-2"></i>Animate Progression
                            </button>
                        </div>
                    </div>

                    <!-- Sensor Status Dashboard -->
                    <div class="dashboard-card mb-4">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0"><i class="fas fa-satellite-dish me-2"></i>Sensor Status</h5>
                        </div>
                        <div class="card-body">
                            <?php $__currentLoopData = $sensorData; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $sensor): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="mb-3">
                                    <div class="d-flex justify-content-between align-items-center mb-1">
                                        <small class="fw-bold"><?php echo e($sensor['name']); ?></small>
                                        <span class="sensor-status status-<?php echo e(strtolower($sensor['status'])); ?>">
                                            <?php echo e($sensor['status']); ?>

                                        </span>
                                    </div>
                                    <div class="d-flex justify-content-between">
                                        <span class="fw-bold"><?php echo e($sensor['current_value']); ?> <?php echo e($sensor['unit']); ?></span>
                                        <small class="text-muted"><?php echo e($sensor['last_updated']->diffForHumans()); ?></small>
                                    </div>
                                    <div class="progress progress-thin mt-1">
                                        <?php
                                            $percentage = ($sensor['current_value'] / $sensor['threshold_critical']) * 100;
                                            $percentage = min(100, max(0, $percentage));
                                        ?>
                                        <div class="progress-bar
                                            <?php if($sensor['status'] === 'Critical'): ?> bg-danger
                                            <?php elseif($sensor['status'] === 'Warning'): ?> bg-warning
                                            <?php else: ?> bg-success
                                            <?php endif; ?>"
                                            style="width: <?php echo e($percentage); ?>%"></div>
                                    </div>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    </div>

                    <!-- Community Alerts -->
                    <div class="dashboard-card">
                        <div class="card-header bg-warning text-dark">
                            <h5 class="mb-0"><i class="fas fa-exclamation-triangle me-2"></i>Active Alerts</h5>
                        </div>
                        <div class="card-body">
                            <?php $__currentLoopData = $communityAlerts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $alert): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="alert-card dashboard-card mb-3 alert-<?php echo e(strtolower($alert['risk_level'])); ?>">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-start mb-2">
                                            <h6 class="mb-0"><?php echo e($alert['state']); ?> - <?php echo e($alert['lga']); ?></h6>
                                            <span class="badge bg-<?php echo e($alert['risk_level'] === 'High' ? 'danger' : ($alert['risk_level'] === 'Moderate' ? 'warning' : 'success')); ?>">
                                                <?php echo e($alert['alert_type']); ?>

                                            </span>
                                        </div>
                                        <p class="mb-2 small"><?php echo e($alert['description']); ?></p>
                                        <div class="d-flex justify-content-between">
                                            <small class="text-muted">
                                                <i class="fas fa-clock me-1"></i>
                                                Valid until <?php echo e($alert['valid_until']->format('M j, H:i')); ?>

                                            </small>
                                            <small class="text-muted">
                                                <?php echo e(count($alert['communities'])); ?> communities
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    </div>
                </div>

                <!-- Interactive Map -->
                <div class="col-lg-6 mb-4">
                    <div class="dashboard-card">
                        <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                            <h5 class="mb-0"><i class="fas fa-map me-2"></i>Interactive Flood Risk Map</h5>
                            <div class="btn-group">
                                <button class="btn btn-sm btn-light" onclick="toggleBasemap()">
                                    <i class="fas fa-layer-group me-1"></i> Layers
                                </button>
                                <button class="btn btn-sm btn-light" onclick="fullscreenMap()">
                                    <i class="fas fa-expand me-1"></i> Fullscreen
                                </button>
                            </div>
                        </div>
                        <div class="card-body position-relative" style="padding: 0;">
                            <!-- Map Controls -->
                            <div class="map-controls" id="mapControls" style="display: none;">
                                <div class="basemap-selector">
                                    <label class="form-label">Basemap:</label>
                                    <select class="form-select form-select-sm" id="basemapSelector">
                                        <option value="carto" selected>CartoDB Light</option>
                                        <option value="osm">OpenStreetMap</option>
                                        <option value="satellite">Satellite</option>
                                        <option value="terrain">Terrain</option>
                                    </select>
                                </div>
                                <div class="overlay-controls">
                                    <label><input type="checkbox" checked> Flood Risk Areas</label>
                                    <label><input type="checkbox"> Infrastructure</label>
                                    <label><input type="checkbox"> Health Facilities</label>
                                    <label><input type="checkbox"> Schools</label>
                                    <label><input type="checkbox"> Roads</label>
                                    <label><input type="checkbox"> Agricultural Zones</label>
                                </div>
                            </div>

                            <div id="map">
                                <div id="map-loading" class="d-flex justify-content-center align-items-center h-100">
                                    <div class="text-center">
                                        <div class="spinner-border text-primary" role="status">
                                            <span class="visually-hidden">Loading...</span>
                                        </div>
                                        <p class="mt-2 text-muted">Loading map...</p>
                                    </div>
                                </div>
                            </div>

                            <!-- Legend -->
                            <div class="legend">
                                <h6 class="mb-3">Flood Risk Levels</h6>
                                <div class="legend-item">
                                    <div class="legend-color" style="background-color: #ff0000;"></div>
                                    <span>High Risk (<?php echo e($statistics['high_risk']); ?> areas)</span>
                                </div>
                                <div class="legend-item">
                                    <div class="legend-color" style="background-color: #ffa500;"></div>
                                    <span>Moderate Risk (<?php echo e($statistics['moderate_risk']); ?> areas)</span>
                                </div>
                                <div class="legend-item">
                                    <div class="legend-color" style="background-color: #ffff00;"></div>
                                    <span>Low Risk (<?php echo e($statistics['low_risk']); ?> areas)</span>
                                </div>
                                <hr class="my-2">
                                <div class="legend-item">
                                    <div class="legend-color" style="background-color: #0066cc;"></div>
                                    <span>Riverine Flooding</span>
                                </div>
                                <div class="legend-item">
                                    <div class="legend-color" style="background-color: #9900cc;"></div>
                                    <span>Flash/Urban Flooding</span>
                                </div>
                                <div class="legend-item">
                                    <div class="legend-color" style="background-color: #00cc66;"></div>
                                    <span>Coastal Flooding</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Temporal Forecasting & Data Tables -->
                <div class="col-lg-3 mb-4">
                    <!-- 7-Day Forecast -->
                    <div class="dashboard-card mb-4">
                        <div class="card-header bg-secondary text-white">
                            <h5 class="mb-0"><i class="fas fa-cloud-rain me-2"></i>7-Day Forecast</h5>
                        </div>
                        <div class="card-body">
                            <?php $__currentLoopData = $forecastData; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $forecast): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="forecast-day">
                                    <div class="forecast-icon">
                                        <?php if($forecast['flood_risk'] === 'High'): ?>
                                            <i class="fas fa-exclamation-triangle text-danger"></i>
                                        <?php elseif($forecast['flood_risk'] === 'Moderate'): ?>
                                            <i class="fas fa-exclamation-circle text-warning"></i>
                                        <?php else: ?>
                                            <i class="fas fa-check-circle text-success"></i>
                                        <?php endif; ?>
                                    </div>
                                    <h6><?php echo e($forecast['day_name']); ?></h6>
                                    <p class="mb-1"><strong><?php echo e($forecast['expected_rainfall']); ?>mm</strong></p>
                                    <p class="mb-1"><?php echo e($forecast['rainfall_probability']); ?>% chance</p>
                                    <span class="badge bg-<?php echo e($forecast['flood_risk'] === 'High' ? 'danger' : ($forecast['flood_risk'] === 'Moderate' ? 'warning' : 'success')); ?>">
                                        <?php echo e($forecast['flood_risk']); ?> Risk
                                    </span>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    </div>

                    <!-- Quick Stats -->
                    <div class="dashboard-card">
                        <div class="card-header bg-dark text-white">
                            <h5 class="mb-0"><i class="fas fa-chart-bar me-2"></i>Quick Stats</h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <div class="d-flex justify-content-between">
                                    <span>Population at Risk:</span>
                                    <strong><?php echo e(number_format($statistics['total_population_at_risk'])); ?></strong>
                                </div>
                            </div>
                            <div class="mb-3">
                                <div class="d-flex justify-content-between">
                                    <span>Affected Area:</span>
                                    <strong><?php echo e(number_format($statistics['total_affected_area'], 1)); ?> km²</strong>
                                </div>
                            </div>
                            <div class="mb-3">
                                <div class="d-flex justify-content-between">
                                    <span>States Affected:</span>
                                    <strong><?php echo e($statistics['states_affected']); ?></strong>
                                </div>
                            </div>
                            <div class="mb-3">
                                <div class="d-flex justify-content-between">
                                    <span>LGAs Affected:</span>
                                    <strong><?php echo e($statistics['lgas_affected']); ?></strong>
                                </div>
                            </div>
                            <hr>
                            <div class="text-center">
                                <button class="btn btn-outline-primary btn-sm" onclick="downloadReport()">
                                    <i class="fas fa-download me-1"></i> Download Report
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Simple Clean Map Section -->
    <section class="py-5 bg-white">
        <div class="container-fluid">
            <div class="row mb-4">
                <div class="col-12 text-center">
                    <h2 class="mb-3">Simple Nigeria Map View</h2>
                    <p class="text-muted">Clean map view without markers - Just Nigeria geography</p>
                </div>
            </div>
            <div class="row">
                <div class="col-12">
                    <div class="card shadow-sm">
                        <div class="card-header bg-success text-white d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="fas fa-map me-2"></i>Clean Map View
                            </h5>
                            <button class="btn btn-light btn-sm" onclick="initSimpleMap()">
                                <i class="fas fa-play me-1"></i>Load Simple Map
                            </button>
                        </div>
                        <div class="card-body p-0">
                            <div id="simple-map" style="height: 500px; width: 100%; background-color: #f8f9fa;">
                                <div class="d-flex justify-content-center align-items-center h-100">
                                    <div class="text-center">
                                        <i class="fas fa-map text-success" style="font-size: 48px;"></i>
                                        <p class="mt-3 text-muted">Click "Load Simple Map" to view Nigeria without any markers</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Data Tables & Charts Section -->
    <section class="py-5 bg-light">
        <div class="container-fluid">
            <div class="row mb-4">
                <div class="col-12 text-center">
                    <h2 class="mb-3">Detailed Analysis & Reports</h2>
                    <p class="text-muted">Community-level data and hydrological time series analysis</p>
                </div>
            </div>
            <div class="row">
                <!-- Community-Level Alerts & Listings -->
                <div class="col-lg-6 mb-4">
                    <div class="dashboard-card">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0"><i class="fas fa-list me-2"></i>Community-Level Risk Listings</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover data-table">
                                    <thead>
                                        <tr>
                                            <th>State</th>
                                            <th>LGA</th>
                                            <th>Community</th>
                                            <th>Risk Level</th>
                                            <th>Flood Type</th>
                                            <th>Population</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php $__currentLoopData = $floodData; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $data): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <tr>
                                                <td><?php echo e($data['state']); ?></td>
                                                <td><?php echo e($data['lga']); ?></td>
                                                <td><?php echo e($data['community']); ?></td>
                                                <td>
                                                    <span class="badge bg-<?php echo e($data['risk'] === 'High' ? 'danger' : ($data['risk'] === 'Moderate' ? 'warning' : 'success')); ?>">
                                                        <?php echo e($data['risk']); ?>

                                                    </span>
                                                </td>
                                                <td><?php echo e($data['flood_type']); ?></td>
                                                <td><?php echo e(number_format($data['affected_population'])); ?></td>
                                                <td>
                                                    <button class="btn btn-sm btn-outline-primary" onclick="viewDetails(<?php echo e($data['id']); ?>)">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-info" onclick="zoomToLocation(<?php echo e($data['lat']); ?>, <?php echo e($data['lng']); ?>)">
                                                        <i class="fas fa-map-marker-alt"></i>
                                                    </button>
                                                </td>
                                            </tr>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Hydrographs & Time Series -->
                <div class="col-lg-6 mb-4">
                    <div class="dashboard-card">
                        <div class="card-header bg-info text-white">
                            <h5 class="mb-0"><i class="fas fa-chart-line me-2"></i>Rainfall & Water Level Time Series</h5>
                        </div>
                        <div class="card-body">
                            <canvas id="timeSeriesChart" height="300"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Help & Methodology Section -->
    <section class="py-5">
        <div class="container">
            <div class="row mb-4">
                <div class="col-12 text-center">
                    <h2 class="mb-3">About This Dashboard</h2>
                    <p class="text-muted">Technical information and support resources</p>
                </div>
            </div>
            <div class="row">
                <div class="col-lg-10 mx-auto">
                    <div class="dashboard-card">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>Dashboard Information</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6 mb-4">
                                    <h6 class="text-primary"><i class="fas fa-database me-2"></i>Data Sources</h6>
                                    <ul class="list-unstyled">
                                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Rainfall Models (ECMWF, GFS)</li>
                                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Hydrodynamic Simulations</li>
                                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Real-time Sensor Networks</li>
                                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Satellite Observations</li>
                                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Ground-based Weather Stations</li>
                                    </ul>
                                </div>
                                <div class="col-md-6 mb-4">
                                    <h6 class="text-primary"><i class="fas fa-clock me-2"></i>Update Frequency</h6>
                                    <ul class="list-unstyled">
                                        <li class="mb-2"><i class="fas fa-sync text-info me-2"></i>Forecasts: Daily at 06:00 UTC</li>
                                        <li class="mb-2"><i class="fas fa-sync text-info me-2"></i>Sensor Data: Every 15 minutes</li>
                                        <li class="mb-2"><i class="fas fa-sync text-info me-2"></i>Alerts: Real-time</li>
                                        <li class="mb-2"><i class="fas fa-sync text-info me-2"></i>Maps: Every 6 hours</li>
                                        <li class="mb-2"><i class="fas fa-sync text-info me-2"></i>Statistics: Hourly</li>
                                    </ul>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-4">
                                    <h6 class="text-primary"><i class="fas fa-shield-alt me-2"></i>Data Quality</h6>
                                    <ul class="list-unstyled">
                                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Quality controlled observations</li>
                                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Validated forecast models</li>
                                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Peer-reviewed methodologies</li>
                                    </ul>
                                </div>
                                <div class="col-md-6 mb-4">
                                    <h6 class="text-primary"><i class="fas fa-users me-2"></i>Target Users</h6>
                                    <ul class="list-unstyled">
                                        <li class="mb-2"><i class="fas fa-user-tie text-warning me-2"></i>Emergency Management Agencies</li>
                                        <li class="mb-2"><i class="fas fa-user-tie text-warning me-2"></i>Water Resource Managers</li>
                                        <li class="mb-2"><i class="fas fa-user-tie text-warning me-2"></i>Research Institutions</li>
                                        <li class="mb-2"><i class="fas fa-user-tie text-warning me-2"></i>Government Officials</li>
                                    </ul>
                                </div>
                            </div>

                            <hr class="my-4">
                            <div class="text-center">
                                <h6 class="mb-3">Need Support or Have Questions?</h6>
                                <p class="text-muted mb-4">Our technical team is available to assist with data interpretation and system usage.</p>
                                <div class="d-flex flex-wrap justify-content-center gap-2">
                                    <a href="mailto:<EMAIL>" class="btn btn-primary">
                                        <i class="fas fa-envelope me-2"></i> Email Support
                                    </a>
                                    <a href="<?php echo e(route('contact.index')); ?>" class="btn btn-outline-primary">
                                        <i class="fas fa-phone me-2"></i> Contact Us
                                    </a>
                                    <a href="<?php echo e(route('about')); ?>" class="btn btn-outline-secondary">
                                        <i class="fas fa-info-circle me-2"></i> About NIHSA
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js" ></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    <script>
        // Global variables
        let map;
        let simpleMap;
        let floodMarkers = [];
        let currentBasemap = 'osm';
        let isAnimating = false;

        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded, initializing dashboard...');

            // Check if Leaflet is loaded
            if (typeof L === 'undefined') {
                console.error('Leaflet library not loaded!');
                document.getElementById('map').innerHTML = '<div class="alert alert-danger">Map library failed to load. Please refresh the page.</div>';
                return;
            }

            console.log('Leaflet version:', L.version);

            try {
                initializeMap();
                console.log('Map initialized');

                initializeCharts();
                console.log('Charts initialized');

                initializeEventListeners();
                console.log('Event listeners initialized');

                // Load flood data after a short delay to ensure map is ready
                setTimeout(() => {
                    loadFloodData();
                    console.log('Flood data loading...');
                }, 1000);

                // Call handleMobileView after map is initialized
                handleMobileView();

            } catch (error) {
                console.error('Error during initialization:', error);

                // Try to reinitialize after a delay
                setTimeout(() => {
                    console.log('Retrying map initialization...');
                    try {
                        initializeMap();
                        setTimeout(() => loadFloodData(), 1000);
                    } catch (retryError) {
                        console.error('Retry failed:', retryError);
                        document.getElementById('map').innerHTML = '<div class="alert alert-danger">Map failed to load. Please refresh the page or try a different browser.</div>';
                    }
                }, 2000);
            }
        });

        function initializeMap() {
            try {
                console.log('Creating map instance...');

                // Clear any existing content
                document.getElementById('map').innerHTML = '';

                // Initialize the map exactly like the working test page
                map = L.map('map').setView([9.0820, 8.6753], 6);

                console.log('Map instance created, adding tiles...');

                // Add tile layer exactly like the working test page
                L.tileLayer('https://{s}.basemaps.cartocdn.com/light_all/{z}/{x}/{y}{r}.png', {
                    attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors &copy; <a href="https://carto.com/attributions">CARTO</a>',
                    maxZoom: 19,
                    subdomains: 'abcd'
                }).addTo(map);

                console.log('Tile layer added successfully');

                console.log('Map initialized successfully - clean map without markers');

                // Add map controls
                addMapControls();

                // Force a resize after a short delay
                setTimeout(() => {
                    map.invalidateSize();
                    console.log('Map size invalidated');
                }, 100);

            } catch (error) {
                console.error('Error initializing map:', error);
                // Show error message to user
                document.getElementById('map').innerHTML = '<div class="alert alert-danger">Error loading map: ' + error.message + '</div>';
            }
        }

        // Initialize simple clean map without any markers
        function initSimpleMap() {
            try {
                console.log('Initializing simple clean map...');

                // Clear the container
                document.getElementById('simple-map').innerHTML = '';

                // Create simple map centered on Nigeria
                simpleMap = L.map('simple-map', {
                    center: [9.0820, 8.6753], // Nigeria center coordinates
                    zoom: 6,
                    zoomControl: true,        // Keep zoom controls
                    attributionControl: true, // Keep attribution
                    dragging: true,           // Allow dragging
                    scrollWheelZoom: true,    // Allow mouse wheel zoom
                    doubleClickZoom: true,    // Allow double-click zoom
                    touchZoom: true           // Allow touch zoom
                });

                // Add clean tile layer (same as working test)
                L.tileLayer('https://{s}.basemaps.cartocdn.com/light_all/{z}/{x}/{y}{r}.png', {
                    attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors &copy; <a href="https://carto.com/attributions">CARTO</a>',
                    maxZoom: 19,
                    subdomains: 'abcd'
                }).addTo(simpleMap);

                console.log('Simple clean map initialized successfully - NO MARKERS');

                // Force resize after initialization
                setTimeout(() => {
                    simpleMap.invalidateSize();
                    console.log('Simple map size invalidated');
                }, 100);

            } catch (error) {
                console.error('Error initializing simple map:', error);
                document.getElementById('simple-map').innerHTML = '<div class="alert alert-danger p-3">Error loading simple map: ' + error.message + '</div>';
            }
        }

        // Simple basemap changer (more reliable)
        function changeBasemap(type) {
            try {
                // Remove all existing tile layers
                map.eachLayer(function(layer) {
                    if (layer._url) {
                        map.removeLayer(layer);
                    }
                });

                let tileUrl, attribution;

                switch(type) {
                    case 'carto':
                        tileUrl = 'https://{s}.basemaps.cartocdn.com/light_all/{z}/{x}/{y}{r}.png';
                        attribution = '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors &copy; <a href="https://carto.com/attributions">CARTO</a>';
                        break;
                    case 'satellite':
                        tileUrl = 'https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}';
                        attribution = 'Tiles &copy; Esri';
                        break;
                    case 'terrain':
                        tileUrl = 'https://{s}.tile.opentopomap.org/{z}/{x}/{y}.png';
                        attribution = 'Map data: &copy; OpenStreetMap contributors, SRTM | Map style: &copy; OpenTopoMap';
                        break;
                    default: // osm
                        tileUrl = 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png';
                        attribution = '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors';
                }

                L.tileLayer(tileUrl, {
                    attribution: attribution,
                    maxZoom: 19,
                    subdomains: type === 'carto' ? 'abcd' : ['a', 'b', 'c']
                }).addTo(map);

                console.log('Basemap changed to:', type);

            } catch (error) {
                console.error('Error changing basemap:', error);
            }
        }

        function addBasemap(type) {
            // Remove existing basemap
            map.eachLayer(function(layer) {
                if (layer._url) {
                    map.removeLayer(layer);
                }
            });

            let tileLayer;
            switch(type) {
                case 'carto':
                    // CartoDB Light basemap (reliable alternative)
                    tileLayer = L.tileLayer('https://{s}.basemaps.cartocdn.com/light_all/{z}/{x}/{y}{r}.png', {
                        attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors &copy; <a href="https://carto.com/attributions">CARTO</a>',
                        maxZoom: 19,
                        subdomains: 'abcd'
                    });
                    break;
                case 'satellite':
                    // Using Esri World Imagery
                    tileLayer = L.tileLayer('https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}', {
                        attribution: 'Tiles &copy; Esri &mdash; Source: Esri, i-cubed, USDA, USGS, AEX, GeoEye, Getmapping, Aerogrid, IGN, IGP, UPR-EGP, and the GIS User Community',
                        maxZoom: 18
                    });
                    break;
                case 'terrain':
                    // Using OpenTopoMap
                    tileLayer = L.tileLayer('https://{s}.tile.opentopomap.org/{z}/{x}/{y}.png', {
                        attribution: 'Map data: &copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors, <a href="http://viewfinderpanoramas.org">SRTM</a> | Map style: &copy; <a href="https://opentopomap.org">OpenTopoMap</a> (<a href="https://creativecommons.org/licenses/by-sa/3.0/">CC-BY-SA</a>)',
                        maxZoom: 17
                    });
                    break;
                default:
                    // Try OpenStreetMap first, with CartoDB as fallback
                    try {
                        tileLayer = L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                            attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
                            maxZoom: 19,
                            subdomains: ['a', 'b', 'c']
                        });
                    } catch (error) {
                        console.log('OpenStreetMap failed, trying CartoDB...');
                        tileLayer = L.tileLayer('https://{s}.basemaps.cartocdn.com/light_all/{z}/{x}/{y}{r}.png', {
                            attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors &copy; <a href="https://carto.com/attributions">CARTO</a>',
                            maxZoom: 19,
                            subdomains: 'abcd'
                        });
                    }
            }

            // Add error handling
            tileLayer.on('tileerror', function(error, tile) {
                console.log('Tile loading error:', error);
            });

            tileLayer.addTo(map);
            currentBasemap = type;

            // Force map refresh
            setTimeout(() => {
                map.invalidateSize();
            }, 100);
        }

        function addMapControls() {
            // Add zoom control
            L.control.zoom({
                position: 'topright'
            }).addTo(map);

            // Add scale control
            L.control.scale().addTo(map);
        }

        function loadFloodData() {
            // Get current filter values
            const filters = {
                year: document.getElementById('year').value,
                period: document.getElementById('period').value,
                state: document.getElementById('state').value,
                risk_level: document.getElementById('risk_level').value,
                flood_type: document.getElementById('flood_type').value,
                admin_level: document.getElementById('admin_level').value
            };

            // Load flood data from server
            fetch('<?php echo e(route("flood-forecast-dashboard.api.data")); ?>?' + new URLSearchParams({
                type: 'flood_data',
                ...filters
            }))
            .then(response => response.json())
            .then(data => {
                displayFloodMarkers(data);
            })
            .catch(error => {
                console.error('Error loading flood data:', error);
            });
        }

        function displayFloodMarkers(floodData) {
            // Clear existing markers
            floodMarkers.forEach(marker => map.removeLayer(marker));
            floodMarkers = [];

            // Add new markers
            floodData.forEach(function(point) {
                let color, borderColor;
                switch(point.risk) {
                    case 'High':
                        color = '#ff0000';
                        borderColor = '#cc0000';
                        break;
                    case 'Moderate':
                        color = '#ffa500';
                        borderColor = '#cc8400';
                        break;
                    case 'Low':
                        color = '#ffff00';
                        borderColor = '#cccc00';
                        break;
                    default:
                        color = '#0000ff';
                        borderColor = '#0000cc';
                }

                // Adjust marker style based on flood type
                let radius = 8;
                if (point.flood_type === 'Coastal') {
                    color = '#00cc66';
                    borderColor = '#009944';
                } else if (point.flood_type === 'Flash/Urban') {
                    color = '#9900cc';
                    borderColor = '#770099';
                    radius = 6;
                } else if (point.flood_type === 'Riverine') {
                    color = '#0066cc';
                    borderColor = '#004499';
                    radius = 10;
                }

                const marker = L.circleMarker([point.lat, point.lng], {
                    radius: radius,
                    fillColor: color,
                    color: borderColor,
                    weight: 2,
                    opacity: 1,
                    fillOpacity: 0.8
                }).addTo(map);

                // Create popup content
                const popupContent = `
                    <div class="flood-popup">
                        <h6><strong>${point.state} - ${point.lga}</strong></h6>
                        <p><strong>Community:</strong> ${point.community}</p>
                        <p><strong>Risk Level:</strong> <span class="badge bg-${point.risk === 'High' ? 'danger' : (point.risk === 'Moderate' ? 'warning' : 'success')}">${point.risk}</span></p>
                        <p><strong>Flood Type:</strong> ${point.flood_type}</p>
                        <p><strong>Probability:</strong> ${point.probability}%</p>
                        <p><strong>Population at Risk:</strong> ${point.affected_population.toLocaleString()}</p>
                        <p><strong>Affected Area:</strong> ${point.affected_area} km²</p>
                        <p><strong>Expected Rainfall:</strong> ${point.expected_rainfall}mm</p>
                        <hr>
                        <p class="mb-0">${point.description}</p>
                        <div class="mt-2">
                            <button class="btn btn-sm btn-primary" onclick="viewDetails(${point.id})">
                                <i class="fas fa-info-circle me-1"></i> Details
                            </button>
                            <button class="btn btn-sm btn-warning" onclick="sendAlert(${point.id})">
                                <i class="fas fa-bell me-1"></i> Alert
                            </button>
                        </div>
                    </div>
                `;

                marker.bindPopup(popupContent, {
                    maxWidth: 300,
                    className: 'flood-popup-container'
                });

                floodMarkers.push(marker);
            });
        }

        function initializeCharts() {
            // Initialize time series chart
            const ctx = document.getElementById('timeSeriesChart').getContext('2d');
            const timeSeriesChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
                    datasets: [{
                        label: 'Rainfall (mm)',
                        data: [45, 52, 78, 125, 180, 220, 280, 320, 250, 150, 80, 55],
                        borderColor: 'rgb(75, 192, 192)',
                        backgroundColor: 'rgba(75, 192, 192, 0.2)',
                        tension: 0.1,
                        yAxisID: 'y'
                    }, {
                        label: 'Water Level (m)',
                        data: [2.1, 2.3, 2.8, 3.2, 4.1, 4.8, 5.2, 5.8, 4.9, 3.5, 2.8, 2.2],
                        borderColor: 'rgb(255, 99, 132)',
                        backgroundColor: 'rgba(255, 99, 132, 0.2)',
                        tension: 0.1,
                        yAxisID: 'y1'
                    }]
                },
                options: {
                    responsive: true,
                    interaction: {
                        mode: 'index',
                        intersect: false,
                    },
                    scales: {
                        x: {
                            display: true,
                            title: {
                                display: true,
                                text: 'Month'
                            }
                        },
                        y: {
                            type: 'linear',
                            display: true,
                            position: 'left',
                            title: {
                                display: true,
                                text: 'Rainfall (mm)'
                            }
                        },
                        y1: {
                            type: 'linear',
                            display: true,
                            position: 'right',
                            title: {
                                display: true,
                                text: 'Water Level (m)'
                            },
                            grid: {
                                drawOnChartArea: false,
                            },
                        }
                    }
                }
            });
        }

        function initializeEventListeners() {
            // Filter form submission
            document.getElementById('filterForm').addEventListener('submit', function(e) {
                e.preventDefault();
                loadFloodData();
                updateStatistics();
            });

            // Time slider
            document.getElementById('time_range').addEventListener('input', function(e) {
                const hour = e.target.value;
                document.getElementById('current_time').textContent = hour.padStart(2, '0') + ':00';
                updateMapForTime(hour);
            });

            // Basemap selector
            document.getElementById('basemapSelector').addEventListener('change', function(e) {
                changeBasemap(e.target.value);
            });

            // Date picker
            flatpickr("#forecast_date", {
                dateFormat: "Y-m-d",
                defaultDate: "today",
                onChange: function(selectedDates, dateStr, instance) {
                    updateForecastForDate(dateStr);
                }
            });
        }

        // Export functions
        function exportData(format) {
            const filters = {
                year: document.getElementById('year').value,
                period: document.getElementById('period').value,
                state: document.getElementById('state').value,
                risk_level: document.getElementById('risk_level').value,
                flood_type: document.getElementById('flood_type').value,
                admin_level: document.getElementById('admin_level').value,
                format: format
            };

            const url = '<?php echo e(route("flood-forecast-dashboard.export")); ?>?' + new URLSearchParams(filters);
            window.open(url, '_blank');
        }

        function shareUrl() {
            const filters = {
                year: document.getElementById('year').value,
                period: document.getElementById('period').value,
                state: document.getElementById('state').value,
                risk_level: document.getElementById('risk_level').value,
                flood_type: document.getElementById('flood_type').value,
                admin_level: document.getElementById('admin_level').value
            };

            const url = window.location.origin + window.location.pathname + '?' + new URLSearchParams(filters);

            if (navigator.share) {
                navigator.share({
                    title: 'NIHSA Flood Forecast Dashboard',
                    text: 'Check out the current flood risk situation in Nigeria',
                    url: url
                });
            } else {
                // Fallback: copy to clipboard
                navigator.clipboard.writeText(url).then(function() {
                    alert('URL copied to clipboard!');
                });
            }
        }

        // Map interaction functions
        function toggleBasemap() {
            const controls = document.getElementById('mapControls');
            controls.style.display = controls.style.display === 'none' ? 'block' : 'none';
        }

        function fullscreenMap() {
            const mapContainer = document.getElementById('map').parentElement.parentElement;
            if (mapContainer.requestFullscreen) {
                mapContainer.requestFullscreen();
            } else if (mapContainer.webkitRequestFullscreen) {
                mapContainer.webkitRequestFullscreen();
            } else if (mapContainer.msRequestFullscreen) {
                mapContainer.msRequestFullscreen();
            }

            // Resize map after fullscreen
            setTimeout(() => {
                map.invalidateSize();
            }, 100);
        }

        function zoomToLocation(lat, lng) {
            map.setView([lat, lng], 12);

            // Find and open the popup for this location
            floodMarkers.forEach(marker => {
                const markerLatLng = marker.getLatLng();
                if (Math.abs(markerLatLng.lat - lat) < 0.001 && Math.abs(markerLatLng.lng - lng) < 0.001) {
                    marker.openPopup();
                }
            });
        }

        // Animation functions
        function animateTimeProgression() {
            if (isAnimating) {
                isAnimating = false;
                return;
            }

            isAnimating = true;
            const slider = document.getElementById('time_range');
            let currentHour = 0;

            const interval = setInterval(() => {
                if (!isAnimating || currentHour > 23) {
                    clearInterval(interval);
                    isAnimating = false;
                    return;
                }

                slider.value = currentHour;
                document.getElementById('current_time').textContent = currentHour.toString().padStart(2, '0') + ':00';
                updateMapForTime(currentHour);
                currentHour++;
            }, 500);
        }

        function updateMapForTime(hour) {
            // Update map visualization based on time
            // This would typically involve updating the data displayed on the map
            // For demo purposes, we'll just update the opacity of markers
            const opacity = 0.3 + (Math.sin(hour * Math.PI / 12) + 1) * 0.35;
            floodMarkers.forEach(marker => {
                marker.setStyle({ fillOpacity: opacity });
            });
        }

        // Data interaction functions
        function viewDetails(floodId) {
            // This would typically open a modal or navigate to a detail page
            alert(`Viewing details for flood area ID: ${floodId}`);
        }

        function sendAlert(floodId) {
            // This would typically send an alert to relevant authorities
            if (confirm('Send alert for this flood risk area?')) {
                alert(`Alert sent for flood area ID: ${floodId}`);
            }
        }

        function downloadReport() {
            // Generate and download a comprehensive report
            exportData('pdf');
        }

        function updateStatistics() {
            // Update statistics based on current filters
            // This would typically make an AJAX call to get updated stats
            console.log('Updating statistics...');
        }

        function updateForecastForDate(dateStr) {
            // Update forecast data for selected date
            console.log('Updating forecast for date:', dateStr);
        }

        // User Authentication/API functions (if needed)
        function authenticateUser() {
            // Handle user authentication for API access
            console.log('Authenticating user...');
        }

        // Mobile responsiveness
        function handleMobileView() {
            if (window.innerWidth < 768) {
                document.getElementById('map').style.height = '400px';
                if (typeof map !== 'undefined' && map) {
                    map.invalidateSize();
                }
            }
        }

        // Event listeners for responsive design
        window.addEventListener('resize', handleMobileView);
        window.addEventListener('orientationchange', function() {
            setTimeout(() => {
                map.invalidateSize();
            }, 100);
        });

        // Initialize mobile view on load
        handleMobileView();
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Documents\augment-projects\Nihsa\nihsa-laravel\resources\views/flood-forecast-dashboard/index.blade.php ENDPATH**/ ?>